/**
 * <PERSON><PERSON><PERSON> to update components that use mock data to use real API data instead
 * This script identifies components with hardcoded mock data and provides instructions
 * on how to update them to use real API data.
 */

const mockComponents = [
  {
    path: 'src/components/contracts/ElectronicSignature.tsx',
    issue: 'Uses hardcoded mock signers data',
    solution: `
    // Replace hardcoded signers with API call
    const { fetch } = useApi();
    const [signers, setSigners] = useState([]);
    
    useEffect(() => {
      const fetchSigners = async () => {
        const result = await fetch(
          () => api.get(\`/contracts/\${contractId}/signers\`),
          "Loading signers...",
          "Failed to load signers"
        );
        
        if (result) {
          setSigners(result);
        }
      };
      
      fetchSigners();
    }, [contractId]);
    `
  },
  {
    path: 'src/components/signature/ElectronicSignatureFlow.tsx',
    issue: 'Uses mock document and user data',
    solution: `
    // Replace mock user with real user data
    const { user } = useUser();
    const [currentUser, setCurrentUser] = useState(null);
    const [document, setDocument] = useState(null);
    
    useEffect(() => {
      // Get current user details
      if (user) {
        setCurrentUser({
          id: user.id,
          name: \`\${user.firstName} \${user.lastName}\`,
          email: user.primaryEmailAddress?.emailAddress,
          avatar: user.imageUrl
        });
      }
      
      // Fetch document data
      const fetchDocument = async () => {
        if (!documentId) return;
        
        const result = await fetch(
          () => api.get(\`/documents/\${documentId}\`),
          "Loading document...",
          "Failed to load document"
        );
        
        if (result) {
          setDocument(result);
        }
      };
      
      fetchDocument();
    }, [user, documentId]);
    `
  },
  {
    path: 'src/components/analytics/analyticsData.ts',
    issue: 'Contains extensive mock analytics data',
    solution: `
    // Replace with API service functions
    
    import { api } from '@/lib/api';
    
    // Function to fetch contract activity data
    export async function getContractActivityData(workspaceId: string): Promise<ContractActivityData[]> {
      try {
        const response = await api.get(\`/analytics/contract-activity?workspace_id=\${workspaceId}\`);
        return response.data;
      } catch (error) {
        console.error("Error fetching contract activity data:", error);
        return [];
      }
    }
    
    // Function to fetch contract types data
    export async function getContractTypesData(workspaceId: string): Promise<ContractTypeData[]> {
      try {
        const response = await api.get(\`/analytics/contract-types?workspace_id=\${workspaceId}\`);
        return response.data;
      } catch (error) {
        console.error("Error fetching contract types data:", error);
        return [];
      }
    }
    
    // Function to fetch analytics summary
    export async function getAnalyticsSummary(workspaceId: string): Promise<AnalyticsSummary | null> {
      try {
        const response = await api.get(\`/analytics/summary?workspace_id=\${workspaceId}\`);
        return response.data;
      } catch (error) {
        console.error("Error fetching analytics summary:", error);
        return null;
      }
    }
    
    // Function to fetch performance metrics
    export async function getPerformanceMetrics(workspaceId: string): Promise<PerformanceMetric[]> {
      try {
        const response = await api.get(\`/analytics/performance-metrics?workspace_id=\${workspaceId}\`);
        return response.data;
      } catch (error) {
        console.error("Error fetching performance metrics:", error);
        return [];
      }
    }
    `
  },
  {
    path: 'src/components/contracts/ModernClauseLibrary.tsx',
    issue: 'Uses mock clauses data',
    solution: `
    // Replace mock clauses with API call
    const { fetch } = useApi();
    const [clauses, setClauses] = useState([]);
    const [loading, setLoading] = useState(true);
    
    useEffect(() => {
      const fetchClauses = async () => {
        setLoading(true);
        
        const result = await fetch(
          () => api.get('/clauses'),
          "Loading clause library...",
          "Failed to load clauses"
        );
        
        if (result) {
          setClauses(result);
        }
        
        setLoading(false);
      };
      
      fetchClauses();
    }, []);
    `
  }
];

console.log('Components using mock data that need to be updated:');
console.log('==================================================');

mockComponents.forEach((component, index) => {
  console.log(`\n${index + 1}. ${component.path}`);
  console.log(`   Issue: ${component.issue}`);
  console.log('   Suggested solution:');
  console.log(component.solution);
});

console.log('\n\nAdditional steps:');
console.log('1. Ensure the backend API endpoints exist for all the data being requested');
console.log('2. Update the API service types to match the real API response structure');
console.log('3. Add proper error handling and loading states for all API calls');
console.log('4. Test each component after updating to ensure it works with real data');
console.log('5. Remove any remaining mock data imports or declarations');
