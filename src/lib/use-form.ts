import { useState, useCallback, ChangeEvent, FormEvent } from "react";
import { FieldValidation, ValidationErrors, validateForm, hasErrors } from "./form-validation";

interface UseFormOptions<T> {
  initialValues: T;
  validationSchema?: FieldValidation;
  onSubmit?: (values: T) => void | Promise<void>;
}

interface UseFormReturn<T> {
  values: T;
  errors: ValidationErrors;
  touched: Record<keyof T, boolean>;
  isSubmitting: boolean;
  handleChange: (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
  handleBlur: (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
  handleSubmit: (e: FormEvent<HTMLFormElement>) => void;
  setFieldValue: (field: keyof T, value: any) => void;
  setFieldTouched: (field: keyof T, isTouched?: boolean) => void;
  resetForm: () => void;
  validateField: (field: keyof T) => string[];
  validateForm: () => boolean;
}

export function useForm<T extends Record<string, any>>({
  initialValues,
  validationSchema = {},
  onSubmit,
}: UseFormOptions<T>): UseFormReturn<T> {
  const [values, setValues] = useState<T>(initialValues);
  const [errors, setErrors] = useState<ValidationErrors>({});
  const [touched, setTouched] = useState<Record<keyof T, boolean>>(() => {
    const touchedFields: Record<string, boolean> = {};
    Object.keys(initialValues).forEach((key) => {
      touchedFields[key] = false;
    });
    return touchedFields as Record<keyof T, boolean>;
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Validate a single field
  const validateFieldFn = useCallback(
    (field: keyof T): string[] => {
      if (!validationSchema[field as string]) return [];
      
      const fieldErrors = validateForm({ [field]: values[field] }, { [field as string]: validationSchema[field as string] });
      return fieldErrors[field as string] || [];
    },
    [values, validationSchema]
  );

  // Validate the entire form
  const validateFormFn = useCallback((): boolean => {
    const formErrors = validateForm(values, validationSchema);
    setErrors(formErrors);
    return !hasErrors(formErrors);
  }, [values, validationSchema]);

  // Handle input changes
  const handleChange = useCallback(
    (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
      const { name, value, type } = e.target;
      
      // Handle different input types
      let parsedValue: any = value;
      if (type === "number") {
        parsedValue = value === "" ? "" : Number(value);
      } else if (type === "checkbox") {
        parsedValue = (e.target as HTMLInputElement).checked;
      }
      
      setValues((prev) => ({ ...prev, [name]: parsedValue }));
      
      // Validate field on change if it's been touched
      if (touched[name as keyof T]) {
        const fieldErrors = validateFieldFn(name as keyof T);
        setErrors((prev) => ({
          ...prev,
          [name]: fieldErrors,
        }));
      }
    },
    [touched, validateFieldFn]
  );

  // Handle input blur
  const handleBlur = useCallback(
    (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
      const { name } = e.target;
      
      // Mark field as touched
      setTouched((prev) => ({ ...prev, [name]: true }));
      
      // Validate field on blur
      const fieldErrors = validateFieldFn(name as keyof T);
      setErrors((prev) => ({
        ...prev,
        [name]: fieldErrors,
      }));
    },
    [validateFieldFn]
  );

  // Handle form submission
  const handleSubmit = useCallback(
    async (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      
      // Mark all fields as touched
      const allTouched: Record<string, boolean> = {};
      Object.keys(values).forEach((key) => {
        allTouched[key] = true;
      });
      setTouched(allTouched as Record<keyof T, boolean>);
      
      // Validate all fields
      const isValid = validateFormFn();
      
      if (isValid && onSubmit) {
        setIsSubmitting(true);
        try {
          await onSubmit(values);
        } finally {
          setIsSubmitting(false);
        }
      }
    },
    [values, validateFormFn, onSubmit]
  );

  // Set a field value programmatically
  const setFieldValue = useCallback((field: keyof T, value: any) => {
    setValues((prev) => ({ ...prev, [field]: value }));
    
    // Validate field if it's been touched
    if (touched[field]) {
      const fieldErrors = validateFieldFn(field);
      setErrors((prev) => ({
        ...prev,
        [field]: fieldErrors,
      }));
    }
  }, [touched, validateFieldFn]);

  // Set a field's touched state programmatically
  const setFieldTouched = useCallback((field: keyof T, isTouched = true) => {
    setTouched((prev) => ({ ...prev, [field]: isTouched }));
    
    // Validate field if being marked as touched
    if (isTouched) {
      const fieldErrors = validateFieldFn(field);
      setErrors((prev) => ({
        ...prev,
        [field]: fieldErrors,
      }));
    }
  }, [validateFieldFn]);

  // Reset form to initial values
  const resetForm = useCallback(() => {
    setValues(initialValues);
    setErrors({});
    
    const resetTouched: Record<string, boolean> = {};
    Object.keys(initialValues).forEach((key) => {
      resetTouched[key] = false;
    });
    setTouched(resetTouched as Record<keyof T, boolean>);
  }, [initialValues]);

  return {
    values,
    errors,
    touched,
    isSubmitting,
    handleChange,
    handleBlur,
    handleSubmit,
    setFieldValue,
    setFieldTouched,
    resetForm,
    validateField: validateFieldFn,
    validateForm: validateFormFn,
  };
}
