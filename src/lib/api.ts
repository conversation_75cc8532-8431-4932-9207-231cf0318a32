import { useError } from "./error-provider";
import { useLoading } from "./loading-provider";
import { useAuth } from "@clerk/clerk-react";
import { useCallback } from "react";

// API response types
export interface ApiResponse<T> {
  data: T;
  status: number;
  message: string;
}

export interface ApiError {
  status: number;
  message: string;
  details?: string;
  code?: string;
}

// Base API service with error handling
export class ApiService {
  private baseUrl: string;
  private activeWorkspaceId: string | null = null;

  constructor(baseUrl = "http://localhost:8000/api") {
    this.baseUrl = baseUrl;
  }

  // Set the active workspace ID
  setWorkspaceId(workspaceId: string | null) {
    this.activeWorkspaceId = workspaceId;
  }

  // Get the active workspace ID
  getWorkspaceId(): string | null {
    return this.activeWorkspaceId;
  }

  // Generic request method with error handling
  async request<T>(
    endpoint: string,
    options: RequestInit = {},
    authToken?: string
  ): Promise<ApiResponse<T>> {
    try {
      const url = `${this.baseUrl}${endpoint}`;

      // Set default headers
      const headers: Record<string, string> = {
        "Content-Type": "application/json",
        ...(options.headers as Record<string, string> || {}),
      };

      // Add authentication header if available
      if (authToken) {
        headers["Authorization"] = `Bearer ${authToken}`;
      }

      // Add workspace ID header if available
      if (this.activeWorkspaceId) {
        headers["X-Workspace-ID"] = this.activeWorkspaceId;
      }

      // Add authentication token if available
      try {
        // Get the token from Clerk if it's available in the window object
        if (window.Clerk && window.Clerk.session) {
          const token = await window.Clerk.session.getToken();
          if (token) {
            headers["Authorization"] = `Bearer ${token}`;
          }
        } else {
          // Use development token if Clerk is not available
          console.warn("Clerk not available, using development token");
          headers["Authorization"] = `Bearer dev-token`;
        }
      } catch (authError) {
        console.warn("Failed to get auth token:", authError);
        // Use development token as fallback
        headers["Authorization"] = `Bearer dev-token`;
      }

      // Make the request
      const response = await fetch(url, {
        ...options,
        headers,
      });

      // Parse the response
      let data;
      try {
        // Check if the response has content before trying to parse it
        const contentType = response.headers.get("content-type");
        if (contentType && contentType.includes("application/json")) {
          const text = await response.text();
          // Only try to parse if there's actual content
          if (text.trim()) {
            try {
              data = JSON.parse(text);
            } catch (jsonError) {
              console.error("JSON parse error:", jsonError, "Response text:", text);
              throw {
                status: response.status,
                message: "Invalid JSON response from server",
                details: `Failed to parse: ${text.substring(0, 100)}${text.length > 100 ? '...' : ''}`,
                code: "PARSE_ERROR",
              };
            }
          } else {
            // Empty response
            data = {};
          }
        } else {
          // Not a JSON response
          const text = await response.text();
          console.warn("Non-JSON response:", text);
          data = { message: text || "No content" };
        }
      } catch (parseError) {
        // Handle JSON parsing errors
        console.error("Error parsing API response:", parseError);
        throw {
          status: response.status,
          message: "Invalid response format from server",
          details: "The server response could not be parsed as JSON",
          code: "PARSE_ERROR",
        };
      }

      // Check if the response is successful
      if (!response.ok) {
        throw {
          status: response.status,
          message: data?.message || "An error occurred",
          details: data?.details || "No additional details provided",
          code: data?.code,
        };
      }

      // Debug the response data
      console.log("API raw response data:", data);

      // Handle different response formats
      // If data is already in the expected format with a data property, use it directly
      if (data && typeof data === 'object' && 'data' in data) {
        return {
          data: data.data,
          status: response.status,
          message: data.message || "Success",
        };
      }

      // Otherwise, treat the entire response as the data
      // Ensure arrays are properly handled
      return {
        data,
        status: response.status,
        message: "Success",
      };
    } catch (error) {
      // Format the error
      const apiError: ApiError = {
        status: (error as any)?.status || 500,
        message: (error as any)?.message || "An unexpected error occurred",
        details: (error as any)?.details || "No additional details available",
        code: (error as any)?.code || "UNKNOWN_ERROR",
      };

      throw apiError;
    }
  }

  // Convenience methods for different HTTP methods
  async get<T>(endpoint: string, options: RequestInit = {}, authToken?: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: "GET" }, authToken);
  }

  async post<T>(
    endpoint: string,
    data: any,
    options: RequestInit = {},
    authToken?: string
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      ...options,
      method: "POST",
      body: JSON.stringify(data),
    }, authToken);
  }

  async put<T>(
    endpoint: string,
    data: any,
    options: RequestInit = {},
    authToken?: string
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      ...options,
      method: "PUT",
      body: JSON.stringify(data),
    }, authToken);
  }

  async delete<T>(
    endpoint: string,
    options: RequestInit = {},
    authToken?: string
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: "DELETE" }, authToken);
  }
}

// Helper function to ensure data is an array
export function ensureArray<T>(data: T | T[] | null | undefined): T[] {
  if (!data) return [];
  return Array.isArray(data) ? data : [data];
}

// Using real API
export const api = new ApiService();

// Hook for using the API with loading and error handling
export function useApi() {
  const { handleApiError } = useError();
  const { withLoading } = useLoading();
  const { getToken } = useAuth();

  // Memoize the fetch function to prevent infinite re-renders
  const fetch = useCallback(async <T>(
    apiCall: (token?: string) => Promise<ApiResponse<T>>,
    loadingMessage = "Loading...",
    errorMessage = "Failed to fetch data"
  ): Promise<T | null> => {
    try {
      // Get the authentication token
      let token: string | null = null;
      try {
        token = await getToken();
      } catch (authError) {
        console.warn("Failed to get Clerk token, using development token:", authError);
        token = "dev-token";
      }

      // Use development token if no token is available
      if (!token) {
        token = "dev-token";
      }

      const response = await withLoading(apiCall(token), loadingMessage);
      // Debug the API response
      console.log("API Response:", response);

      // Check if response.data exists and return it
      if (response && response.data) {
        // If the endpoint is known to return an array, ensure it's an array
        const url = (apiCall as any).toString();
        if (url.includes('/clauses') && !Array.isArray(response.data)) {
          console.error("Expected array for clauses endpoint but got:", response.data);
          return [] as unknown as T;
        }
        return response.data;
      }

      // If response exists but data is missing, log an error
      if (response) {
        console.error("API response missing data property:", response);
      }

      return null;
    } catch (error: any) {
      // Don't treat cancelled requests as errors
      if (error.name === 'AbortError' || error.message?.includes('cancelled')) {
        console.log("🚫 API: Request was cancelled");
        return null;
      }

      console.error("API fetch error:", error);
      handleApiError(error, errorMessage);
      return null;
    }
  }, [getToken, withLoading, handleApiError]);

  return {
    fetch,

    // Memoize the fetchArray function to prevent infinite re-renders
    fetchArray: useCallback(async <T>(
      apiCall: () => Promise<ApiResponse<T | T[]>>,
      loadingMessage = "Loading...",
      errorMessage = "Failed to fetch data"
    ): Promise<T[]> => {
      try {
        const response = await withLoading(apiCall(), loadingMessage);
        // Debug the API response
        console.log("API Array Response:", response);

        // Check if response.data exists and ensure it's an array
        if (response && response.data) {
          return ensureArray(response.data);
        }

        // If response exists but data is missing, log an error
        if (response) {
          console.error("API response missing data property:", response);
        }

        return [];
      } catch (error: any) {
        // Don't treat cancelled requests as errors
        if (error.name === 'AbortError' || error.message?.includes('cancelled')) {
          console.log("🚫 API: Array request was cancelled");
          return [];
        }

        console.error("API fetch array error:", error);
        handleApiError(error, errorMessage);
        return [];
      }
    }, [withLoading, handleApiError]),

    // Raw API service for more control
    api,
  };
}

// For development and testing, we also provide a mock API
export class MockApiService extends ApiService {
  private mockData: Record<string, any>;
  private delay: number;

  constructor(options: { mockData?: Record<string, any>; delay?: number } = {}) {
    super();
    this.mockData = options.mockData || {};
    this.delay = options.delay || 500;
  }

  // Override request method to use mock data
  async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      // Simulate network delay
      await new Promise((resolve) => setTimeout(resolve, this.delay));

      // Get the mock data for this endpoint
      const mockEndpoint = endpoint.split("?")[0]; // Remove query params
      const data = this.mockData[mockEndpoint];

      // Simulate error if no mock data is found
      if (!data && options.method !== "POST") {
        throw {
          status: 404,
          message: `No mock data found for ${mockEndpoint}`,
          details: "The requested resource does not exist in mock data",
          code: "NOT_FOUND",
        };
      }

      // For POST requests without mock data, create a default success response
      if (!data && options.method === "POST") {
        return {
          data: { id: "mock-id-" + Date.now(), success: true } as unknown as T,
          status: 201,
          message: "Created successfully",
        };
      }

      // Return the mock data
      return {
        data: data as T,
        status: 200,
        message: "Success",
      };
    } catch (error) {
      // Format the error consistently with the real API service
      const apiError: ApiError = {
        status: (error as any)?.status || 500,
        message: (error as any)?.message || "An unexpected error occurred in mock API",
        details: (error as any)?.details || "No additional details available",
        code: (error as any)?.code || "MOCK_ERROR",
      };

      throw apiError;
    }
  }

  // Add mock data
  addMockData(endpoint: string, data: any): void {
    this.mockData[endpoint] = data;
  }
}

// Create a mock API instance for frontend development
export const mockApi = new MockApiService({
  mockData: {
    // Add your mock data here
    "/contracts": [
      {
        id: "c1",
        title: "Service Agreement with Acme Corp",
        type: "Service Agreement",
        status: "active",
      },
      {
        id: "c2",
        title: "Non-Disclosure Agreement with TechStart",
        type: "NDA",
        status: "active",
      },
    ],
    // Add mock clauses data
    "/clauses": [
      {
        id: "cl-001",
        title: "Standard Limitation of Liability",
        content: "In no event shall either party be liable to the other party for any indirect, special, incidental, consequential, or punitive damages.",
        category: "liability",
        tags: ["limitation", "standard"],
        lastUpdated: "2023-05-15",
        version: "1.2",
        approved: true,
        author: "Jane Smith"
      },
      {
        id: "cl-002",
        title: "Basic Confidentiality Clause",
        content: "During the term of this Agreement and for a period of three (3) years thereafter, each party shall maintain in confidence all Confidential Information of the other party.",
        category: "confidentiality",
        tags: ["nda", "standard"],
        lastUpdated: "2023-04-10",
        version: "1.0",
        approved: true,
        author: "Sarah Johnson"
      }
    ]
  }
});
