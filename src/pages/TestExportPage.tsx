import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import DocumentExportButton from '@/components/contracts/DocumentExportButton';
import BatchExportButton from '@/components/contracts/BatchExportButton';
import DocumentExportManager from '@/components/repository/DocumentExportManager';
import WorkspaceBrandingSettings from '@/components/workspace/WorkspaceBrandingSettings';
import ExportHistoryManager from '@/components/exports/ExportHistoryManager';
import { ContractService } from '@/services/api-services';
import { useAuth } from '@clerk/clerk-react';
import { useToast } from '@/components/ui/use-toast';
import { 
  FileText, 
  Package, 
  TestTube, 
  CheckCircle, 
  AlertCircle,
  Loader2 
} from 'lucide-react';

const TestExportPage: React.FC = () => {
  const [testContractId, setTestContractId] = useState('test-contract-123');
  const [testContractIds, setTestContractIds] = useState(['test-contract-123', 'test-contract-456']);
  const [isCreatingTestContract, setIsCreatingTestContract] = useState(false);
  const [createdContractId, setCreatedContractId] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'export' | 'repository' | 'branding' | 'history'>('export');
  const { toast } = useToast();
  const { getToken } = useAuth();

  const createTestContract = async () => {
    setIsCreatingTestContract(true);
    try {
      const token = await getToken();
      
      const testContractData = {
        title: 'Test Export Contract',
        type: 'service_agreement',
        workspace_id: 'default-workspace',
        description: 'This is a test contract created for testing the export functionality.',
        parties: [
          {
            name: 'LegalAI Inc.',
            type: 'company',
            address: '123 Tech Street, San Francisco, CA 94105',
            role: 'Service Provider'
          },
          {
            name: 'Test Client Corp.',
            type: 'company', 
            address: '456 Business Ave, New York, NY 10001',
            role: 'Client'
          }
        ],
        clauses: [
          {
            title: 'Service Description',
            content: 'The Service Provider agrees to provide contract management services to the Client.'
          }
        ],
        standard_clauses: ['confidentiality', 'limitation_of_liability'],
        effective_date: '2024-01-01T00:00:00Z',
        jurisdiction: 'California, USA'
      };

      const response = await ContractService.createContract(testContractData, token || undefined);
      
      if (response.success && response.data) {
        setCreatedContractId(response.data.id);
        setTestContractId(response.data.id);
        
        toast({
          title: 'Test Contract Created',
          description: `Contract created with ID: ${response.data.id}`,
        });
      } else {
        throw new Error(response.message || 'Failed to create test contract');
      }
    } catch (error: any) {
      console.error('Error creating test contract:', error);
      toast({
        title: 'Error',
        description: error.message || 'Failed to create test contract',
        variant: 'destructive',
      });
    } finally {
      setIsCreatingTestContract(false);
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <div className="space-y-6">
        {/* Header */}
        <div className="text-center space-y-2">
          <h1 className="text-3xl font-bold flex items-center justify-center gap-2">
            <TestTube className="h-8 w-8" />
            Document Export Test Suite
          </h1>
          <p className="text-muted-foreground">
            Test all document generation and export functionality
          </p>
        </div>

        {/* Navigation Tabs */}
        <div className="flex justify-center">
          <div className="flex space-x-1 bg-muted p-1 rounded-lg">
            <button
              onClick={() => setActiveTab('export')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                activeTab === 'export'
                  ? 'bg-background text-foreground shadow-sm'
                  : 'text-muted-foreground hover:text-foreground'
              }`}
            >
              Export Testing
            </button>
            <button
              onClick={() => setActiveTab('repository')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                activeTab === 'repository'
                  ? 'bg-background text-foreground shadow-sm'
                  : 'text-muted-foreground hover:text-foreground'
              }`}
            >
              Repository Manager
            </button>
            <button
              onClick={() => setActiveTab('branding')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                activeTab === 'branding'
                  ? 'bg-background text-foreground shadow-sm'
                  : 'text-muted-foreground hover:text-foreground'
              }`}
            >
              Branding Settings
            </button>
            <button
              onClick={() => setActiveTab('history')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                activeTab === 'history'
                  ? 'bg-background text-foreground shadow-sm'
                  : 'text-muted-foreground hover:text-foreground'
              }`}
            >
              Export History
            </button>
          </div>
        </div>

        {/* Tab Content */}
        {activeTab === 'export' && (
          <div className="space-y-6">
            {/* Test Contract Creation */}
            <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Create Test Contract
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-muted-foreground">
              Create a test contract in the database to test export functionality.
            </p>
            
            <Button 
              onClick={createTestContract}
              disabled={isCreatingTestContract}
              className="w-full"
            >
              {isCreatingTestContract ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Creating Test Contract...
                </>
              ) : (
                <>
                  <FileText className="h-4 w-4 mr-2" />
                  Create Test Contract
                </>
              )}
            </Button>

            {createdContractId && (
              <div className="flex items-center gap-2 p-3 bg-green-50 rounded-lg border border-green-200">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <span className="text-sm font-medium text-green-800">
                  Test contract created: {createdContractId}
                </span>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Single Contract Export Test */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Single Contract Export
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="contractId">Contract ID</Label>
              <Input
                id="contractId"
                value={testContractId}
                onChange={(e) => setTestContractId(e.target.value)}
                placeholder="Enter contract ID to test export"
              />
            </div>
            
            <div className="flex items-center gap-4">
              <DocumentExportButton
                contractId={testContractId}
                contractTitle="Test Export Contract"
                variant="default"
                size="default"
                showLabel={true}
              />
              <span className="text-sm text-muted-foreground">
                Click to test single contract export in multiple formats
              </span>
            </div>
          </CardContent>
        </Card>

        {/* Batch Export Test */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Batch Contract Export
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="contractIds">Contract IDs (comma-separated)</Label>
              <Input
                id="contractIds"
                value={testContractIds.join(', ')}
                onChange={(e) => setTestContractIds(e.target.value.split(',').map(id => id.trim()).filter(Boolean))}
                placeholder="Enter contract IDs separated by commas"
              />
            </div>
            
            <div className="flex items-center gap-4">
              <BatchExportButton
                contractIds={testContractIds}
                contractTitles={testContractIds.map(id => `Test Contract ${id}`)}
                variant="default"
                size="default"
              />
              <span className="text-sm text-muted-foreground">
                Click to test batch export of multiple contracts
              </span>
            </div>
          </CardContent>
        </Card>

        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5" />
              Test Instructions
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="space-y-2 text-sm">
              <p><strong>1. Create Test Contract:</strong> Click the button above to create a real contract in the database.</p>
              <p><strong>2. Test Single Export:</strong> Use the contract ID to test individual document export in various formats (PDF, DOCX, HTML, TXT, Markdown).</p>
              <p><strong>3. Test Batch Export:</strong> Enter multiple contract IDs to test batch export functionality.</p>
              <p><strong>4. Check Downloads:</strong> Exported documents should automatically download to your browser's download folder.</p>
            </div>
            
            <Separator />
            
            <div className="text-xs text-muted-foreground space-y-1">
              <p><strong>Backend Status:</strong> Make sure the backend is running on port 8000</p>
              <p><strong>Authentication:</strong> Uses development authentication bypass</p>
              <p><strong>PDF Engine:</strong> Using ReportLab fallback (WeasyPrint not available)</p>
            </div>
          </CardContent>
        </Card>
          </div>
        )}

        {/* Repository Manager Tab */}
        {activeTab === 'repository' && (
          <DocumentExportManager showFilters={true} />
        )}

        {/* Branding Settings Tab */}
        {activeTab === 'branding' && (
          <WorkspaceBrandingSettings />
        )}

        {/* Export History Tab */}
        {activeTab === 'history' && (
          <ExportHistoryManager />
        )}
      </div>
    </div>
  );
};

export default TestExportPage;
