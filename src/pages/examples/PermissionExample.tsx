import React from 'react';
import { usePermissionCheck } from '@/hooks/usePermissionCheck';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Shield, ShieldAlert, ShieldCheck, Lock, Key } from 'lucide-react';
import AccessDeniedDialog from '@/components/permissions/AccessDeniedDialog';

const PermissionExample = () => {
  const {
    checkPermission,
    checkAnyPermission,
    isDialogOpen,
    dialogProps,
    closeDialog,
  } = usePermissionCheck({
    featureTitle: 'Permission Example',
  });

  // Function to handle a protected action
  const handleProtectedAction = async (permissionId: string, actionName: string) => {
    const hasPermission = await checkPermission(permissionId, {
      featureTitle: actionName,
    });
    
    if (hasPermission) {
      // This code will only run if the user has permission
      console.log(`User has permission to ${actionName}`);
      alert(`Success! You have permission to ${actionName}`);
    }
    // If the user doesn't have permission, the dialog will be shown automatically
  };

  return (
    <div className="container py-8">
      <h1 className="text-3xl font-bold mb-6">Permission Check Example</h1>
      <p className="text-muted-foreground mb-8">
        This page demonstrates how to use the permission check system to protect actions
        without hiding UI elements. Click the buttons below to test different permission checks.
      </p>

      <Tabs defaultValue="basic">
        <TabsList className="mb-6">
          <TabsTrigger value="basic">Basic Checks</TabsTrigger>
          <TabsTrigger value="advanced">Advanced Checks</TabsTrigger>
        </TabsList>

        <TabsContent value="basic">
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {/* Contract View Permission */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5 text-primary" />
                  View Contracts
                </CardTitle>
                <CardDescription>
                  Requires the "contracts.view" permission
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm">
                  This action checks if you have permission to view contracts.
                  The UI is always visible, but the action will be blocked if you don't have permission.
                </p>
              </CardContent>
              <CardFooter>
                <Button 
                  onClick={() => handleProtectedAction('contracts.view', 'View Contracts')}
                  className="w-full"
                >
                  Try to View Contracts
                </Button>
              </CardFooter>
            </Card>

            {/* Contract Edit Permission */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <ShieldCheck className="h-5 w-5 text-primary" />
                  Edit Contracts
                </CardTitle>
                <CardDescription>
                  Requires the "contracts.edit" permission
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm">
                  This action checks if you have permission to edit contracts.
                  The UI is always visible, but the action will be blocked if you don't have permission.
                </p>
              </CardContent>
              <CardFooter>
                <Button 
                  onClick={() => handleProtectedAction('contracts.edit', 'Edit Contracts')}
                  className="w-full"
                >
                  Try to Edit Contracts
                </Button>
              </CardFooter>
            </Card>

            {/* Contract Delete Permission */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <ShieldAlert className="h-5 w-5 text-primary" />
                  Delete Contracts
                </CardTitle>
                <CardDescription>
                  Requires the "contracts.delete" permission
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm">
                  This action checks if you have permission to delete contracts.
                  The UI is always visible, but the action will be blocked if you don't have permission.
                </p>
              </CardContent>
              <CardFooter>
                <Button 
                  variant="destructive"
                  onClick={() => handleProtectedAction('contracts.delete', 'Delete Contracts')}
                  className="w-full"
                >
                  Try to Delete Contracts
                </Button>
              </CardFooter>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="advanced">
          <div className="grid gap-6 md:grid-cols-2">
            {/* Any Permission Check */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Lock className="h-5 w-5 text-primary" />
                  Any Permission Check
                </CardTitle>
                <CardDescription>
                  Requires either "templates.view" or "templates.edit"
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm">
                  This action checks if you have any of the specified permissions.
                  You only need one of the permissions to access this feature.
                </p>
              </CardContent>
              <CardFooter>
                <Button 
                  onClick={async () => {
                    const hasPermission = await checkAnyPermission(
                      ['templates.view', 'templates.edit'],
                      { featureTitle: 'Template Access' }
                    );
                    
                    if (hasPermission) {
                      alert('Success! You have at least one of the required permissions.');
                    }
                  }}
                  className="w-full"
                >
                  Check Any Permission
                </Button>
              </CardFooter>
            </Card>

            {/* Admin Action */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Key className="h-5 w-5 text-primary" />
                  Admin Action
                </CardTitle>
                <CardDescription>
                  Requires the "workspace.manage" permission
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm">
                  This action checks if you have admin privileges.
                  Only workspace administrators should be able to perform this action.
                </p>
              </CardContent>
              <CardFooter>
                <Button 
                  variant="outline"
                  onClick={() => handleProtectedAction('workspace.manage', 'Admin Action')}
                  className="w-full"
                >
                  Try Admin Action
                </Button>
              </CardFooter>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* The AccessDeniedDialog is automatically shown when a permission check fails */}
      <AccessDeniedDialog
        open={isDialogOpen}
        onClose={closeDialog}
        featureTitle={dialogProps.featureTitle}
        requiredPermission={dialogProps.requiredPermission}
        customMessage={dialogProps.customMessage}
      />
    </div>
  );
};

export default PermissionExample;
