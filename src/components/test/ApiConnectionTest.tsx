import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>ircle, XCircle, Loader2, RefreshCw } from 'lucide-react';
import { useAuth } from '@clerk/clerk-react';

interface HealthCheckResponse {
  status: string;
  database: string;
  storage: string;
  database_url: string;
  counts: {
    users: number;
    workspaces: number;
    contracts: number;
  };
}

interface AuthTestResponse {
  message: string;
  user: {
    id: string;
    email: string;
    name: string;
    workspaces: string[];
  };
  environment: string;
  clerk_configured: boolean;
}

const ApiConnectionTest: React.FC = () => {
  const [healthStatus, setHealthStatus] = useState<HealthCheckResponse | null>(null);
  const [authStatus, setAuthStatus] = useState<AuthTestResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { getToken, isSignedIn } = useAuth();

  const testHealthEndpoint = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch('http://localhost:8000/api/health');
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      setHealthStatus(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to connect to backend');
      setHealthStatus(null);
    } finally {
      setLoading(false);
    }
  };

  const testAuthEndpoint = async () => {
    try {
      setLoading(true);
      setError(null);
      
      if (!isSignedIn) {
        throw new Error('User is not signed in');
      }

      const token = await getToken();
      if (!token) {
        throw new Error('Failed to get authentication token');
      }

      const response = await fetch('http://localhost:8000/api/auth/test', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      setAuthStatus(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to test authentication');
      setAuthStatus(null);
    } finally {
      setLoading(false);
    }
  };

  const runAllTests = async () => {
    await testHealthEndpoint();
    if (isSignedIn) {
      await testAuthEndpoint();
    }
  };

  useEffect(() => {
    runAllTests();
  }, [isSignedIn]);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'connected':
        return <Badge variant="default" className="bg-green-500"><CheckCircle className="w-3 h-3 mr-1" />Connected</Badge>;
      case 'error':
        return <Badge variant="destructive"><XCircle className="w-3 h-3 mr-1" />Error</Badge>;
      default:
        return <Badge variant="secondary">Unknown</Badge>;
    }
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">API Connection Test</h1>
        <Button onClick={runAllTests} disabled={loading}>
          {loading ? (
            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
          ) : (
            <RefreshCw className="w-4 h-4 mr-2" />
          )}
          Test Connection
        </Button>
      </div>

      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="text-red-800">Connection Error</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-red-700">{error}</p>
          </CardContent>
        </Card>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Health Check */}
        <Card>
          <CardHeader>
            <CardTitle>Backend Health Check</CardTitle>
            <CardDescription>Tests basic backend connectivity and database status</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {healthStatus ? (
              <>
                <div className="flex items-center justify-between">
                  <span>Backend Status:</span>
                  {getStatusBadge(healthStatus.status)}
                </div>
                <div className="flex items-center justify-between">
                  <span>Database:</span>
                  {getStatusBadge(healthStatus.database)}
                </div>
                <div className="flex items-center justify-between">
                  <span>Storage:</span>
                  {getStatusBadge(healthStatus.storage)}
                </div>
                <div className="pt-4 border-t">
                  <h4 className="font-medium mb-2">Database Counts:</h4>
                  <div className="grid grid-cols-3 gap-2 text-sm">
                    <div className="text-center">
                      <div className="font-bold text-lg">{healthStatus.counts.users}</div>
                      <div className="text-muted-foreground">Users</div>
                    </div>
                    <div className="text-center">
                      <div className="font-bold text-lg">{healthStatus.counts.workspaces}</div>
                      <div className="text-muted-foreground">Workspaces</div>
                    </div>
                    <div className="text-center">
                      <div className="font-bold text-lg">{healthStatus.counts.contracts}</div>
                      <div className="text-muted-foreground">Contracts</div>
                    </div>
                  </div>
                </div>
              </>
            ) : (
              <div className="text-center py-4">
                <p className="text-muted-foreground">No health data available</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Authentication Test */}
        <Card>
          <CardHeader>
            <CardTitle>Authentication Test</CardTitle>
            <CardDescription>Tests authenticated API endpoints with Clerk integration</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {!isSignedIn ? (
              <div className="text-center py-4">
                <p className="text-muted-foreground">Please sign in to test authentication</p>
              </div>
            ) : authStatus ? (
              <>
                <div className="flex items-center justify-between">
                  <span>Authentication:</span>
                  <Badge variant="default" className="bg-green-500">
                    <CheckCircle className="w-3 h-3 mr-1" />Authenticated
                  </Badge>
                </div>
                <div className="pt-4 border-t">
                  <h4 className="font-medium mb-2">User Info:</h4>
                  <div className="space-y-2 text-sm">
                    <div><strong>ID:</strong> {authStatus.user.id}</div>
                    <div><strong>Email:</strong> {authStatus.user.email}</div>
                    <div><strong>Name:</strong> {authStatus.user.name}</div>
                    <div><strong>Environment:</strong> {authStatus.environment}</div>
                    <div><strong>Clerk Configured:</strong> {authStatus.clerk_configured ? 'Yes' : 'No'}</div>
                  </div>
                </div>
              </>
            ) : (
              <div className="text-center py-4">
                <p className="text-muted-foreground">No authentication data available</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Connection Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span>Frontend URL:</span>
              <code className="text-sm bg-muted px-2 py-1 rounded">http://localhost:5173</code>
            </div>
            <div className="flex items-center justify-between">
              <span>Backend URL:</span>
              <code className="text-sm bg-muted px-2 py-1 rounded">http://localhost:8000</code>
            </div>
            <div className="flex items-center justify-between">
              <span>Database URL:</span>
              <code className="text-sm bg-muted px-2 py-1 rounded">
                {healthStatus?.database_url || 'Not available'}
              </code>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ApiConnectionTest;
