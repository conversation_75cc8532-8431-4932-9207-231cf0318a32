import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useClerkWorkspace } from '@/lib/clerk-workspace-provider';
import { useToast } from '@/components/ui/use-toast';
import { Loader2, CheckCircle, XCircle, Database, Users } from 'lucide-react';

interface TestResult {
  step: string;
  status: 'pending' | 'success' | 'error';
  message: string;
  data?: any;
}

const WorkspaceCreationTest: React.FC = () => {
  const { createWorkspace, userWorkspaces, currentWorkspace, setCurrentWorkspace, fetchUserWorkspaces } = useClerkWorkspace();
  const { toast } = useToast();
  
  const [formData, setFormData] = useState({
    name: `Test Workspace ${Date.now()}`,
    description: 'A test workspace created from the frontend test component'
  });
  
  const [isLoading, setIsLoading] = useState(false);
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isSwitchingWorkspace, setIsSwitchingWorkspace] = useState(false);

  const addTestResult = (result: TestResult) => {
    setTestResults(prev => [...prev, result]);
  };

  const updateLastTestResult = (updates: Partial<TestResult>) => {
    setTestResults(prev => {
      const newResults = [...prev];
      if (newResults.length > 0) {
        newResults[newResults.length - 1] = { ...newResults[newResults.length - 1], ...updates };
      }
      return newResults;
    });
  };

  const verifyWorkspaceInDatabase = async (workspaceId: string) => {
    try {
      addTestResult({
        step: 'Database Verification',
        status: 'pending',
        message: 'Checking if workspace exists in database...'
      });

      // Call the backend API to verify the workspace exists
      const response = await fetch(`http://localhost:8000/api/workspaces/${workspaceId}`, {
        headers: {
          'Authorization': 'Bearer test_token',
          'X-User-ID': 'dev_user_123'
        }
      });

      if (response.ok) {
        const workspaceData = await response.json();
        updateLastTestResult({
          status: 'success',
          message: 'Workspace found in database!',
          data: workspaceData
        });
        return true;
      } else {
        updateLastTestResult({
          status: 'error',
          message: `Workspace not found in database. Status: ${response.status}`
        });
        return false;
      }
    } catch (error) {
      updateLastTestResult({
        status: 'error',
        message: `Database verification failed: ${error}`
      });
      return false;
    }
  };

  const handleTestWorkspaceCreation = async () => {
    setIsLoading(true);
    setTestResults([]);

    try {
      // Step 1: Create workspace via ClerkWorkspaceProvider
      addTestResult({
        step: 'Workspace Creation',
        status: 'pending',
        message: 'Creating workspace via ClerkWorkspaceProvider...'
      });

      const newWorkspace = await createWorkspace({
        name: formData.name,
        description: formData.description,
        members: 1,
        contracts: 0,
        createdDate: new Date().toISOString().split('T')[0],
      });

      updateLastTestResult({
        status: 'success',
        message: 'Workspace created successfully!',
        data: newWorkspace
      });

      // Step 2: Verify workspace appears in local state
      addTestResult({
        step: 'Local State Verification',
        status: 'pending',
        message: 'Checking if workspace appears in local state...'
      });

      // Wait a moment for state to update
      await new Promise(resolve => setTimeout(resolve, 1000));

      const workspaceInState = userWorkspaces.find(ws => ws.id === newWorkspace.id);
      if (workspaceInState) {
        updateLastTestResult({
          status: 'success',
          message: 'Workspace found in local state!',
          data: workspaceInState
        });
      } else {
        updateLastTestResult({
          status: 'error',
          message: 'Workspace not found in local state'
        });
      }

      // Step 3: Verify workspace in database
      await verifyWorkspaceInDatabase(newWorkspace.id);

      // Step 4: Check if it's set as current workspace
      addTestResult({
        step: 'Current Workspace Check',
        status: 'pending',
        message: 'Checking if workspace is set as current...'
      });

      if (currentWorkspace?.id === newWorkspace.id) {
        updateLastTestResult({
          status: 'success',
          message: 'Workspace is set as current workspace!',
          data: currentWorkspace
        });
      } else {
        updateLastTestResult({
          status: 'error',
          message: 'Workspace is not set as current workspace'
        });
      }

      toast({
        title: "Test Completed",
        description: "Workspace creation test has been completed. Check results below.",
      });

    } catch (error) {
      updateLastTestResult({
        status: 'error',
        message: `Workspace creation failed: ${error}`
      });

      toast({
        title: "Test Failed",
        description: `Workspace creation test failed: ${error}`,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleTestWorkspaceSwitching = async () => {
    if (userWorkspaces.length < 2) {
      toast({
        title: "Insufficient Workspaces",
        description: "You need at least 2 workspaces to test switching functionality.",
        variant: "destructive",
      });
      return;
    }

    setIsSwitchingWorkspace(true);
    setTestResults([]);

    try {
      // Step 1: Test fetching workspaces from backend
      addTestResult({
        step: 'Fetch Workspaces',
        status: 'pending',
        message: 'Fetching workspaces from backend API...'
      });

      const fetchedWorkspaces = await fetchUserWorkspaces();

      updateLastTestResult({
        status: 'success',
        message: `Successfully fetched ${fetchedWorkspaces.length} workspaces from backend`,
        data: fetchedWorkspaces
      });

      // Step 2: Test workspace switching
      const targetWorkspace = userWorkspaces.find(ws => ws.id !== currentWorkspace?.id);
      if (!targetWorkspace) {
        addTestResult({
          step: 'Workspace Switching',
          status: 'error',
          message: 'No alternative workspace found for switching test'
        });
        return;
      }

      addTestResult({
        step: 'Workspace Switching',
        status: 'pending',
        message: `Switching to workspace: ${targetWorkspace.name}...`
      });

      const originalWorkspace = currentWorkspace;
      setCurrentWorkspace(targetWorkspace);

      // Wait a moment for state to update
      await new Promise(resolve => setTimeout(resolve, 1000));

      if (currentWorkspace?.id === targetWorkspace.id) {
        updateLastTestResult({
          status: 'success',
          message: `Successfully switched to workspace: ${targetWorkspace.name}`,
          data: { from: originalWorkspace?.name, to: currentWorkspace.name }
        });
      } else {
        updateLastTestResult({
          status: 'error',
          message: 'Workspace switching failed - current workspace did not update'
        });
      }

      // Step 3: Test switching back
      if (originalWorkspace) {
        addTestResult({
          step: 'Switch Back Test',
          status: 'pending',
          message: `Switching back to original workspace: ${originalWorkspace.name}...`
        });

        setCurrentWorkspace(originalWorkspace);
        await new Promise(resolve => setTimeout(resolve, 1000));

        if (currentWorkspace?.id === originalWorkspace.id) {
          updateLastTestResult({
            status: 'success',
            message: `Successfully switched back to: ${originalWorkspace.name}`,
            data: { switchedBack: true }
          });
        } else {
          updateLastTestResult({
            status: 'error',
            message: 'Failed to switch back to original workspace'
          });
        }
      }

      toast({
        title: "Workspace Switching Test Completed",
        description: "Check results below for detailed information.",
      });

    } catch (error) {
      updateLastTestResult({
        status: 'error',
        message: `Workspace switching test failed: ${error}`
      });

      toast({
        title: "Test Failed",
        description: `Workspace switching test failed: ${error}`,
        variant: "destructive",
      });
    } finally {
      setIsSwitchingWorkspace(false);
    }
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'pending':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
    }
  };

  return (
    <div className="space-y-6 p-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Workspace Creation Test
          </CardTitle>
          <CardDescription>
            Test the complete workspace creation flow from frontend to database
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium">Workspace Name</label>
              <Input
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="Enter workspace name"
              />
            </div>
            <div>
              <label className="text-sm font-medium">Description</label>
              <Textarea
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Enter workspace description"
                rows={3}
              />
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Button
              onClick={handleTestWorkspaceCreation}
              disabled={isLoading || !formData.name.trim()}
              className="w-full"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Testing Creation...
                </>
              ) : (
                'Test Workspace Creation'
              )}
            </Button>

            <Button
              onClick={handleTestWorkspaceSwitching}
              disabled={isSwitchingWorkspace || userWorkspaces.length < 2}
              variant="outline"
              className="w-full"
            >
              {isSwitchingWorkspace ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Testing Switching...
                </>
              ) : (
                'Test Workspace Switching'
              )}
            </Button>
          </div>

          {userWorkspaces.length < 2 && (
            <p className="text-sm text-muted-foreground text-center">
              Create at least 2 workspaces to test switching functionality
            </p>
          )}
        </CardContent>
      </Card>

      {testResults.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Test Results</CardTitle>
            <CardDescription>
              Results of the workspace creation test
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {testResults.map((result, index) => (
                <div key={index} className="flex items-start gap-3 p-3 border rounded-lg">
                  {getStatusIcon(result.status)}
                  <div className="flex-1">
                    <div className="font-medium text-sm">{result.step}</div>
                    <div className="text-sm text-muted-foreground">{result.message}</div>
                    {result.data && (
                      <details className="mt-2">
                        <summary className="text-xs text-blue-600 cursor-pointer">View Data</summary>
                        <pre className="mt-1 text-xs bg-gray-100 p-2 rounded overflow-auto">
                          {JSON.stringify(result.data, null, 2)}
                        </pre>
                      </details>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Current Workspace State
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div><strong>Current Workspace:</strong> {currentWorkspace?.name || 'None'}</div>
            <div><strong>Total Workspaces:</strong> {userWorkspaces.length}</div>
            <div><strong>Workspace IDs:</strong></div>
            <ul className="list-disc list-inside text-sm text-muted-foreground">
              {userWorkspaces.map(ws => (
                <li key={ws.id}>{ws.name} ({ws.id})</li>
              ))}
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default WorkspaceCreationTest;
