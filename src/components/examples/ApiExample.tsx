import React from 'react';
import { useApiQuery, useApiMutation } from '@/lib/use-api-query';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ApiErrorFallback, InlineApiError } from '@/components/ui/api-error-fallback';
import { Skeleton } from '@/components/ui/skeleton';
import { RefreshCw, Plus } from 'lucide-react';

interface DataItem {
  id: string;
  name: string;
  description: string;
}

/**
 * Example component demonstrating the use of API hooks with error handling
 */
export const ApiExample: React.FC = () => {
  // Use our custom API query hook
  const {
    data: items,
    isLoading,
    error,
    refetch,
  } = useApiQuery<DataItem[]>({
    endpoint: '/api/items',
    errorMessage: 'Failed to load items',
    // Transform the data if needed
    transform: (data) => data.map((item: any) => ({
      ...item,
      name: item.name || 'Unnamed Item',
    })),
  });

  // Use our custom API mutation hook
  const {
    mutate: createItem,
    isLoading: isCreating,
    error: createError,
    reset: resetCreateError,
  } = useApiMutation<DataItem, { name: string; description: string }>(
    'post',
    '/api/items',
    {
      loadingMessage: 'Creating item...',
      errorMessage: 'Failed to create item',
      onSuccess: () => {
        // Refetch the list after creating a new item
        refetch();
      },
    }
  );

  const handleCreateItem = () => {
    createItem({
      name: `New Item ${Date.now()}`,
      description: 'This is a new item',
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">Items</h2>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => refetch()}
            disabled={isLoading}
            className="gap-1.5"
          >
            <RefreshCw className={`h-3.5 w-3.5 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button
            size="sm"
            onClick={handleCreateItem}
            disabled={isCreating}
            className="gap-1.5"
          >
            <Plus className="h-3.5 w-3.5" />
            Add Item
          </Button>
        </div>
      </div>

      {/* Show error if API call failed */}
      {error && (
        <ApiErrorFallback
          error={error}
          retry={refetch}
          resetError={refetch}
          title="Failed to load items"
          description="There was an error loading the items. Please try again."
          showDetails={true}
        />
      )}

      {/* Show create error if mutation failed */}
      {createError && (
        <InlineApiError
          error={createError}
          retry={handleCreateItem}
          resetError={resetCreateError}
          title="Failed to create item"
        />
      )}

      {/* Show loading state */}
      {isLoading && (
        <div className="space-y-3">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="overflow-hidden">
              <CardHeader className="pb-2">
                <Skeleton className="h-4 w-1/3" />
                <Skeleton className="h-3 w-2/3" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-10 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Show data when loaded */}
      {!isLoading && items && items.length > 0 && (
        <div className="space-y-3">
          {items.map((item) => (
            <Card key={item.id}>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">{item.name}</CardTitle>
                <CardDescription>{`ID: ${item.id}`}</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">{item.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Show empty state */}
      {!isLoading && (!items || items.length === 0) && !error && (
        <Card className="border-dashed">
          <CardContent className="flex flex-col items-center justify-center py-10 text-center">
            <p className="mb-2 text-sm text-muted-foreground">No items found</p>
            <Button
              variant="outline"
              size="sm"
              onClick={handleCreateItem}
              className="mt-2 gap-1.5"
            >
              <Plus className="h-3.5 w-3.5" />
              Create your first item
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
