import React from "react";
import { ResponsiveLayout, ResponsiveSection } from "@/components/ui/responsive-layout";
import SignatureDemo from "@/components/signature/SignatureDemo";

/**
 * Example page showcasing the electronic signature flow.
 * This page demonstrates how to use the SignatureDemo component.
 */
const SignatureExample: React.FC = () => {
  return (
    <ResponsiveLayout>
      <ResponsiveSection
        title="Electronic Signature Example"
        description="This example demonstrates the electronic signature flow for documents."
      >
        <SignatureDemo />
      </ResponsiveSection>
    </ResponsiveLayout>
  );
};

export default SignatureExample;
