import React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, CardDescription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { FormField } from "@/components/ui/form-field";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useForm } from "@/lib/use-form";
import { required, email, minLength } from "@/lib/form-validation";
import { useError } from "@/lib/error-provider";
import { useLoading } from "@/lib/loading-provider";
import { LoadingButton } from "@/components/ui/loading";
import { ResponsiveLayout, ResponsiveSection, ResponsiveStack } from "@/components/ui/responsive-layout";
import { useNotify } from "@/lib/error-provider";

interface FormValues {
  firstName: string;
  lastName: string;
  email: string;
  company: string;
  message: string;
  agreeToTerms: boolean;
}

const ExampleForm: React.FC = () => {
  const { startLoading, stopLoading } = useLoading();
  const { setError } = useError();
  const notify = useNotify();
  
  const {
    values,
    errors,
    touched,
    handleChange,
    handleBlur,
    handleSubmit,
    isSubmitting,
    resetForm,
  } = useForm<FormValues>({
    initialValues: {
      firstName: "",
      lastName: "",
      email: "",
      company: "",
      message: "",
      agreeToTerms: false,
    },
    validationSchema: {
      firstName: [required("First name is required")],
      lastName: [required("Last name is required")],
      email: [
        required("Email is required"),
        email("Please enter a valid email address"),
      ],
      message: [
        required("Message is required"),
        minLength(10, "Message must be at least 10 characters"),
      ],
      agreeToTerms: [
        {
          validate: (value) => value === true,
          message: "You must agree to the terms and conditions",
        },
      ],
    },
    onSubmit: async (values) => {
      try {
        // Simulate API call
        startLoading();
        
        // Simulate network delay
        await new Promise((resolve) => setTimeout(resolve, 1500));
        
        // Randomly show success or error for demonstration
        const random = Math.random();
        
        if (random > 0.7) {
          // Simulate API error
          throw new Error("Something went wrong with the submission. Please try again.");
        }
        
        // Success
        notify.success("Form submitted successfully!");
        resetForm();
      } catch (error) {
        setError({
          message: (error as Error).message,
          severity: "error",
        });
      } finally {
        stopLoading();
      }
    },
  });

  return (
    <ResponsiveLayout>
      <ResponsiveSection
        title="Example Form with Validation"
        description="This form demonstrates form validation, loading states, and error handling."
      >
        <Card className="max-w-md mx-auto">
          <CardHeader>
            <CardTitle>Contact Form</CardTitle>
            <CardDescription>
              Fill out this form to get in touch with our team.
            </CardDescription>
          </CardHeader>
          
          <form onSubmit={handleSubmit}>
            <CardContent className="space-y-4">
              <ResponsiveStack direction="row" responsive spacing="md">
                <FormField
                  id="firstName"
                  name="firstName"
                  label="First Name"
                  value={values.firstName}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  error={touched.firstName ? errors.firstName : undefined}
                  touched={touched.firstName}
                  required
                />
                
                <FormField
                  id="lastName"
                  name="lastName"
                  label="Last Name"
                  value={values.lastName}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  error={touched.lastName ? errors.lastName : undefined}
                  touched={touched.lastName}
                  required
                />
              </ResponsiveStack>
              
              <FormField
                id="email"
                name="email"
                label="Email"
                type="email"
                value={values.email}
                onChange={handleChange}
                onBlur={handleBlur}
                error={touched.email ? errors.email : undefined}
                touched={touched.email}
                required
                autoComplete="email"
              />
              
              <FormField
                id="company"
                name="company"
                label="Company"
                value={values.company}
                onChange={handleChange}
                onBlur={handleBlur}
                error={touched.company ? errors.company : undefined}
                touched={touched.company}
              />
              
              <FormField
                id="message"
                name="message"
                label="Message"
                type="textarea"
                value={values.message}
                onChange={handleChange}
                onBlur={handleBlur}
                error={touched.message ? errors.message : undefined}
                touched={touched.message}
                required
                rows={4}
              />
              
              <FormField
                id="agreeToTerms"
                name="agreeToTerms"
                label="I agree to the terms and conditions"
                type="checkbox"
                value={values.agreeToTerms}
                onChange={handleChange}
                onBlur={handleBlur}
                error={touched.agreeToTerms ? errors.agreeToTerms : undefined}
                touched={touched.agreeToTerms}
                required
              />
            </CardContent>
            
            <CardFooter className="flex justify-between">
              <Button
                type="button"
                variant="outline"
                onClick={resetForm}
                disabled={isSubmitting}
              >
                Reset
              </Button>
              
              <LoadingButton
                type="submit"
                loading={isSubmitting}
                disabled={isSubmitting}
              >
                Submit
              </LoadingButton>
            </CardFooter>
          </form>
        </Card>
      </ResponsiveSection>
    </ResponsiveLayout>
  );
};

export default ExampleForm;
