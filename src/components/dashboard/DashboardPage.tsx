import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import StatisticsSection from "./StatisticsSection";
import PendingApprovals from "./PendingApprovals";
import RecentActivities from "./RecentActivities";
import { format } from "date-fns";
import {
  AlertCircle,
  ArrowRight,
  CalendarDays,
  Download,
  FilePlus,
  Info,
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { useApi } from "@/lib/api";
import { ContractService, TemplateService } from "@/services/api-services";
import { useClerkWorkspace } from "@/lib/clerk-workspace-provider";
import type { Contract as ApiContract, Template as ApiTemplate } from "@/services/api-types";

// Custom hook to handle navigation
const useRouter = () => {
  const push = (path: string) => {
    console.log(`Navigating to: ${path}`);
    // Actually navigate to the path
    window.location.href = path;
  };

  return { push };
};

// Types
interface User {
  name: string;
  initials: string;
}

interface Contract {
  id: string;
  title: string;
  type: string;
  status: "draft" | "review" | "active" | "expired" | "rejected";
  createdBy: User;
  createdDate: string;
  expiryDate?: string;
  counterparty: string;
  value?: string;
  daysRemaining?: number;
}

interface Activity {
  id: string;
  type: "created" | "edited" | "status_change" | "approval" | "rejection";
  contractName: string;
  user: string;
  timestamp: string;
  details?: string;
}

interface PendingContract {
  id: string;
  title: string;
  submittedBy: {
    name: string;
    initials: string;
  };
  submittedDate: string;
  priority: "high" | "medium" | "low";
  status: "pending" | "reviewing" | "approved" | "rejected";
}

interface Template {
  id: string;
  name: string;
  title: string;
  type: string;
  description: string;
  industry: string;
  complexity: string;
  usageCount: number;
  lastUsed: string;
  createdBy: User;
}

const DashboardPage = () => {
  const router = useRouter();
  const today = new Date();
  const formattedDate = format(today, "EEEE, MMMM d, yyyy");
  const { currentWorkspace } = useClerkWorkspace();
  const { fetch, fetchArray } = useApi();

  // Loading states
  const [loading, setLoading] = useState({
    contracts: true,
    activities: true,
    templates: true,
    summary: true
  });

  // Error states
  const [error] = useState({
    contracts: null as string | null,
    activities: null as string | null,
    templates: null as string | null,
    summary: null as string | null
  });

  // Dashboard data state
  const [dashboardData, setDashboardData] = useState({
    totalContracts: 0,
    pendingApprovals: 0,
    expiringSoon: 0,
    complianceRate: 0,
    recentActivities: [] as Activity[],
    contracts: [] as Contract[],
    pendingContracts: [] as PendingContract[],
    templates: [] as Template[]
  });

  // Calculate days remaining for each contract and populate expiring contracts
  const [expiringContracts, setExpiringContracts] = useState<Contract[]>([]);

  // Fetch dashboard data from API
  useEffect(() => {
    const fetchDashboardData = async () => {
      if (!currentWorkspace) {
        // No workspace selected, show empty state
        setLoading({
          contracts: false,
          activities: false,
          templates: false,
          summary: false
        });
        return;
      }

      try {
        setLoading({
          contracts: true,
          activities: true,
          templates: true,
          summary: true
        });

        // Fetch contracts for the current workspace
        const contractsResult = await fetchArray(
          () => ContractService.getContracts({ workspace_id: currentWorkspace.id }),
          "Loading contracts...",
          "Failed to load contracts"
        );

        // Fetch templates for the current workspace
        const templatesResult = await fetchArray(
          () => TemplateService.getTemplates({ workspace_id: currentWorkspace.id }),
          "Loading templates...",
          "Failed to load templates"
        );

        // Transform contracts data
        if (contractsResult) {
          const transformedContracts: Contract[] = contractsResult.map((contract: ApiContract) => {
            const createdByName = typeof contract.created_by === 'string'
              ? contract.created_by
              : contract.created_by?.name || "Unknown";

            return {
              id: contract.id,
              title: contract.title,
              type: contract.type,
              status: contract.status as "draft" | "review" | "active" | "expired" | "rejected",
              createdBy: {
                name: createdByName,
                initials: createdByName.substring(0, 2).toUpperCase()
              },
              createdDate: contract.created_at || new Date().toISOString(),
              expiryDate: contract.expiry_date,
              counterparty: contract.counterparty || "Unknown",
              value: contract.value,
            };
          });

          // Calculate dashboard metrics
          const totalContracts = transformedContracts.length;
          const pendingApprovals = transformedContracts.filter(c => c.status === "review").length;
          const expiringSoon = transformedContracts.filter(c => {
            if (!c.expiryDate) return false;
            const expiryDate = new Date(c.expiryDate);
            const thirtyDaysFromNow = new Date();
            thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
            return expiryDate <= thirtyDaysFromNow;
          }).length;
          const complianceRate = totalContracts > 0 ? Math.round((transformedContracts.filter(c => c.status === "active").length / totalContracts) * 100) : 0;

          setDashboardData(prev => ({
            ...prev,
            totalContracts,
            pendingApprovals,
            expiringSoon,
            complianceRate,
            contracts: transformedContracts
          }));

          // Set expiring contracts
          const expiring = transformedContracts
            .filter(c => c.expiryDate)
            .map(c => {
              const expiryDate = new Date(c.expiryDate!);
              const today = new Date();
              const daysRemaining = Math.ceil((expiryDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
              return { ...c, daysRemaining };
            })
            .filter(c => c.daysRemaining <= 30 && c.daysRemaining >= 0)
            .sort((a, b) => a.daysRemaining - b.daysRemaining);

          setExpiringContracts(expiring);
        }

        // Transform templates data
        if (templatesResult) {
          const transformedTemplates: Template[] = templatesResult.map((template: ApiTemplate) => {
            const createdByName = typeof template.created_by === 'string'
              ? template.created_by
              : template.created_by?.name || "Unknown";

            return {
              id: template.id,
              name: template.title, // Map title to name for compatibility
              title: template.title,
              type: template.type,
              description: template.description || "",
              industry: template.industry || "General",
              complexity: template.complexity || "Medium",
              usageCount: template.usage_count || 0,
              lastUsed: template.updated_at || template.created_at,
              createdBy: {
                name: createdByName,
                initials: createdByName.substring(0, 2).toUpperCase()
              }
            };
          });

          setDashboardData(prev => ({
            ...prev,
            templates: transformedTemplates
          }));
        }

      } catch (error) {
        console.error("Error fetching dashboard data:", error);
      } finally {
        setLoading({
          contracts: false,
          activities: false,
          templates: false,
          summary: false
        });
      }
    };

    fetchDashboardData();
  }, [currentWorkspace]);



  // Handlers for contract actions
  const handleApproveContract = async (id: string) => {
    console.log(`Approving contract with ID: ${id}`);

    try {
      // Find the contract to get its title
      const contract = dashboardData.pendingContracts.find(c => c.id === id);
      if (!contract) return;

      // Make API call to update contract status
      await fetch(
        () => ContractService.updateContract(id, { status: 'active' }),
        "Approving contract...",
        "Failed to approve contract"
      );

      // Update local state
      const updatedPendingContracts = dashboardData.pendingContracts.filter(
        contract => contract.id !== id
      );

      // Add activity
      const newActivity = {
        id: `activity-${Date.now()}`,
        type: "approval" as const,
        contractName: contract.title,
        user: "Current User", // In a real app, this would be the current user's name
        timestamp: new Date().toISOString(),
      };

      setDashboardData({
        ...dashboardData,
        pendingApprovals: updatedPendingContracts.length,
        pendingContracts: updatedPendingContracts,
        recentActivities: [newActivity, ...dashboardData.recentActivities]
      });

    } catch (err) {
      console.error("Error approving contract:", err);
    }
  };

  const handleRejectContract = async (id: string) => {
    console.log(`Rejecting contract with ID: ${id}`);

    try {
      // Find the contract to get its title
      const contract = dashboardData.pendingContracts.find(c => c.id === id);
      if (!contract) return;

      // Make API call to update contract status
      await fetch(
        () => ContractService.updateContract(id, { status: 'rejected' }),
        "Rejecting contract...",
        "Failed to reject contract"
      );

      // Update local state
      const updatedPendingContracts = dashboardData.pendingContracts.filter(
        contract => contract.id !== id
      );

      // Add activity
      const newActivity = {
        id: `activity-${Date.now()}`,
        type: "rejection" as const,
        contractName: contract.title,
        user: "Current User", // In a real app, this would be the current user's name
        timestamp: new Date().toISOString(),
      };

      setDashboardData({
        ...dashboardData,
        pendingApprovals: updatedPendingContracts.length,
        pendingContracts: updatedPendingContracts,
        recentActivities: [newActivity, ...dashboardData.recentActivities]
      });

    } catch (err) {
      console.error("Error rejecting contract:", err);
    }
  };

  const handleReviewContract = (id: string) => {
    console.log(`Reviewing contract with ID: ${id}`);
    // Navigate to contract details page
    router.push(`/contracts/${id}`);
  };

  // Navigate to contract page
  const handleViewContract = (id: string) => {
    console.log(`Viewing contract with ID: ${id}`);
    router.push(`/contracts/${id}`);
  };

  // Handler for creating new contract
  const handleCreateContract = () => {
    console.log("Creating new contract");
    router.push("/contracts/create");
  };

  // Handler for importing contract
  const handleImportContract = () => {
    console.log("Importing contract");
    router.push("/contracts/import");
  };

  // View all expiring contracts
  const handleViewAllExpiring = () => {
    router.push("/contracts?filter=expiring");
  };

  // View all pending approvals
  const handleViewAllApprovals = () => {
    router.push("/approvals");
  };

  // Format date for display
  const formatDisplayDate = (dateString: string) => {
    const date = new Date(dateString);
    return format(date, "MMM d, yyyy");
  };

  // Render expiring contracts
  const renderExpiringContracts = () => {
    if (loading.contracts) {
      return (
        <div className="space-y-3">
          {[1, 2, 3].map((i) => (
            <div key={i} className="p-3 border rounded-lg">
              <div className="flex items-start justify-between">
                <div>
                  <Skeleton className="h-5 w-40 mb-1" />
                  <Skeleton className="h-3 w-32" />
                </div>
                <Skeleton className="h-6 w-16" />
              </div>
              <div className="mt-2">
                <Skeleton className="h-4 w-24" />
              </div>
            </div>
          ))}
        </div>
      );
    }

    return (
      <div className="space-y-3">
        {expiringContracts.map((contract) => (
          <div
            key={contract.id}
            className="p-3 border rounded-lg hover:bg-muted/50 cursor-pointer"
            onClick={() => handleViewContract(contract.id)}
          >
            <div className="flex items-start justify-between">
              <div>
                <div className="font-medium">{contract.title}</div>
                <div className="text-xs text-muted-foreground">
                  {contract.type} • {contract.counterparty}
                </div>
              </div>
              <Badge
                variant={
                  (contract.daysRemaining || 0) <= 14 ? "destructive" : "outline"
                }
              >
                {contract.daysRemaining} days
              </Badge>
            </div>
            <div className="mt-2 text-sm">
              <span className="text-muted-foreground">Expires:</span>{" "}
              {formatDisplayDate(contract.expiryDate || "")}
            </div>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className="page-container">
      <div className="page-header">
        <div className="space-y-1">
          <h1 className="heading-primary">Dashboard</h1>
          <p className="caption-text">
            Welcome, John! • {formattedDate}
          </p>
        </div>
        <div className="flex items-center spacing-tight">
          <Button variant="enhanced-outline" size="sm" className="flex items-center spacing-micro" onClick={handleImportContract}>
            <Download className="h-4 w-4" />
            Import
          </Button>
          <Button variant="enhanced" size="sm" className="flex items-center spacing-micro" onClick={handleCreateContract}>
            <FilePlus className="h-4 w-4" />
            New Contract
          </Button>
        </div>
      </div>

      {/* Statistics Section */}
      <StatisticsSection
        totalContracts={dashboardData.totalContracts}
        pendingApprovals={dashboardData.pendingApprovals}
        expiringSoon={dashboardData.expiringSoon}
        complianceRate={dashboardData.complianceRate}
        isLoading={loading.summary}
      />

      {/* Expiring Soon and Pending Approvals */}
      <div className="grid-comfortable grid-cols-1 md:grid-cols-2 mt-6">
        {/* Expiring Soon Section */}
        <Card variant="enhanced" interactive>
          <CardHeader className="pb-3">
            <div className="flex flex-row items-center justify-between">
              <CardTitle className="heading-card">Expiring Soon</CardTitle>
              <Button variant="link" className="h-auto p-0 text-xs text-muted-foreground hover:text-primary micro-lift" onClick={handleViewAllExpiring}>
                View All
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {error.contracts ? (
              <div className="flex flex-col items-center justify-center py-12">
                <AlertCircle className="h-10 w-10 text-destructive mb-3" />
                <h3 className="text-lg font-medium mb-1">Error loading contracts</h3>
                <p className="text-sm text-muted-foreground text-center">{error.contracts}</p>
                <Button
                  variant="outline"
                  size="sm"
                  className="mt-4"
                  onClick={() => window.location.reload()}
                >
                  Retry
                </Button>
              </div>
            ) : loading.contracts ? (
              renderExpiringContracts()
            ) : expiringContracts.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-12">
                <div className="rounded-full bg-muted p-3 mb-3">
                  <CalendarDays className="h-10 w-10 text-muted-foreground" />
                </div>
                <h3 className="text-lg font-medium mb-1">No expiring contracts</h3>
                <p className="text-sm text-muted-foreground text-center">
                  All your contracts are in good standing.
                </p>
                <p className="text-sm text-muted-foreground">
                  Contracts expiring within 30 days will appear here.
                </p>
              </div>
            ) : (
              renderExpiringContracts()
            )}
          </CardContent>
        </Card>

        {/* Pending Approvals Section */}
        <Card variant="enhanced" interactive>
          <CardHeader className="pb-3">
            <div className="flex flex-row items-center justify-between">
              <CardTitle className="heading-card">Pending Approvals</CardTitle>
              <Button variant="link" className="h-auto p-0 text-xs text-muted-foreground hover:text-primary micro-lift" onClick={handleViewAllApprovals}>
                View All
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {error.contracts ? (
              <div className="flex flex-col items-center justify-center py-12">
                <AlertCircle className="h-10 w-10 text-destructive mb-3" />
                <h3 className="text-lg font-medium mb-1">Error loading approvals</h3>
                <p className="text-sm text-muted-foreground text-center">{error.contracts}</p>
                <Button
                  variant="outline"
                  size="sm"
                  className="mt-4"
                  onClick={() => window.location.reload()}
                >
                  Retry
                </Button>
              </div>
            ) : dashboardData.pendingContracts.length === 0 && !loading.contracts ? (
              <div className="flex flex-col items-center justify-center py-12">
                <div className="rounded-full bg-muted p-3 mb-3">
                  <Info className="h-10 w-10 text-muted-foreground" />
                </div>
                <h3 className="text-lg font-medium mb-1">No pending approvals</h3>
                <p className="text-sm text-muted-foreground text-center">
                  You're all caught up! There are no contracts
                </p>
                <p className="text-sm text-muted-foreground">
                  waiting for approval.
                </p>
              </div>
            ) : (
              <PendingApprovals
                contracts={dashboardData.pendingContracts}
                onApprove={handleApproveContract}
                onReject={handleRejectContract}
                onReview={handleReviewContract}
                isLoading={loading.contracts}
              />
            )}
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 gap-5 mt-6">
        {/* Recent Activities Section */}
        <Card variant="enhanced" interactive>
          <CardHeader className="pb-3">
            <div className="flex flex-row items-center justify-between">
              <CardTitle className="heading-card">Recent Activities</CardTitle>
              <Button variant="link" className="h-auto p-0 text-xs" onClick={() => router.push("/activity")}>
                View All
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {error.activities ? (
              <div className="flex flex-col items-center justify-center py-12">
                <AlertCircle className="h-10 w-10 text-destructive mb-3" />
                <h3 className="text-lg font-medium mb-1">Error loading activities</h3>
                <p className="text-sm text-muted-foreground text-center">{error.activities}</p>
                <Button
                  variant="outline"
                  size="sm"
                  className="mt-4"
                  onClick={() => window.location.reload()}
                >
                  Retry
                </Button>
              </div>
            ) : dashboardData.recentActivities.length === 0 && !loading.activities ? (
              <div className="flex flex-col items-center justify-center py-12">
                <div className="rounded-full bg-muted p-3 mb-3">
                  <FilePlus className="h-10 w-10 text-muted-foreground" />
                </div>
                <h3 className="text-lg font-medium mb-1">No recent activities</h3>
                <p className="text-sm text-muted-foreground text-center">
                  Create your first contract to see activity here.
                </p>
                <div className="mt-4">
                  <Button className="flex items-center gap-2" onClick={handleCreateContract}>
                    <FilePlus className="h-4 w-4" />
                    Create Contract
                  </Button>
                </div>
              </div>
            ) : (
              <RecentActivities
                activities={dashboardData.recentActivities}
                maxItems={5}
                isLoading={loading.activities}
              />
            )}
          </CardContent>
        </Card>

        {/* Recently Used Templates Section */}
        <Card>
          <CardHeader className="pb-2">
            <div className="flex flex-row items-center justify-between">
              <CardTitle className="text-sm font-medium">Recently Used Templates</CardTitle>
              <Button variant="link" className="h-auto p-0 text-xs" onClick={() => router.push("/contracts/templates")}>
                View All
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {error.templates ? (
              <div className="flex flex-col items-center justify-center py-12">
                <AlertCircle className="h-10 w-10 text-destructive mb-3" />
                <h3 className="text-lg font-medium mb-1">Error loading templates</h3>
                <p className="text-sm text-muted-foreground text-center">{error.templates}</p>
                <Button
                  variant="outline"
                  size="sm"
                  className="mt-4"
                  onClick={() => window.location.reload()}
                >
                  Retry
                </Button>
              </div>
            ) : loading.templates ? (
              <div className="space-y-3">
                {[1, 2, 3].map(i => (
                  <div
                    key={i}
                    className="flex items-center justify-between p-3 border rounded-lg"
                  >
                    <div>
                      <Skeleton className="h-5 w-40 mb-1" />
                      <Skeleton className="h-3 w-32" />
                    </div>
                    <Skeleton className="h-4 w-4 rounded-full" />
                  </div>
                ))}
              </div>
            ) : dashboardData.templates.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-12">
                <div className="rounded-full bg-muted p-3 mb-3">
                  <Info className="h-10 w-10 text-muted-foreground" />
                </div>
                <h3 className="text-lg font-medium mb-1">No templates used yet</h3>
                <p className="text-sm text-muted-foreground text-center">
                  Start using templates to speed up your contract
                </p>
                <p className="text-sm text-muted-foreground">
                  creation process.
                </p>
                <div className="mt-4">
                  <Button variant="outline" className="flex items-center gap-2" onClick={() => router.push("/contracts/templates")}>
                    Browse Templates
                  </Button>
                </div>
              </div>
            ) : (
              <div className="space-y-3">
                {dashboardData.templates.map(template => (
                  <div
                    key={template.id}
                    className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 cursor-pointer"
                    onClick={() => router.push(`/contracts/templates/${template.id}`)}
                  >
                    <div>
                      <div className="font-medium">{template.name}</div>
                      <div className="text-xs text-muted-foreground">
                        Used {template.usageCount} times • Last used on {formatDisplayDate(template.lastUsed)}
                      </div>
                    </div>
                    <ArrowRight className="h-4 w-4 text-muted-foreground" />
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default DashboardPage;