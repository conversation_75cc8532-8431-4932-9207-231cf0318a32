import React from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import DocumentViewer from './DocumentViewer';

interface DocumentViewerModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  documentId: string;
  title?: string;
  readOnly?: boolean;
  onSave?: (content: string) => void;
  onError?: (error: string) => void;
}

const DocumentViewerModal: React.FC<DocumentViewerModalProps> = ({
  open,
  onOpenChange,
  documentId,
  title,
  readOnly = true,
  onSave,
  onError
}) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl h-[90vh] p-0">
        <DialogHeader className="px-6 py-4 border-b">
          <DialogTitle>{title || 'Document Viewer'}</DialogTitle>
        </DialogHeader>
        <div className="h-[calc(90vh-4rem)]">
          <DocumentViewer
            documentId={documentId}
            readOnly={readOnly}
            onSave={onSave}
            onError={onError}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default DocumentViewerModal;
