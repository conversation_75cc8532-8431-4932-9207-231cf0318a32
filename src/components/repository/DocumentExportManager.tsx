import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useToast } from '@/components/ui/use-toast';
import { ContractService } from '@/services/api-services';
import { useAuth } from '@clerk/clerk-react';
import { useClerkWorkspace } from '@/lib/clerk-workspace-provider';
import DocumentExportButton from '../contracts/DocumentExportButton';
import BatchExportButton from '../contracts/BatchExportButton';
import {
  FileText,
  Search,
  Filter,
  Download,
  Calendar,
  User,
  FolderOpen,
  CheckSquare,
  Square,
  RefreshCw,
} from 'lucide-react';

interface DocumentItem {
  id: string;
  title: string;
  type: 'contract' | 'template' | 'document';
  format: string;
  size: number;
  created_at: string;
  created_by: string;
  folder_id?: string;
  tags?: string[];
  status?: string;
}

interface DocumentExportManagerProps {
  folderId?: string;
  showFilters?: boolean;
}

const DocumentExportManager: React.FC<DocumentExportManagerProps> = ({
  folderId,
  showFilters = true,
}) => {
  const [documents, setDocuments] = useState<DocumentItem[]>([]);
  const [selectedDocuments, setSelectedDocuments] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const { toast } = useToast();
  const { getToken } = useAuth();
  const { currentWorkspace } = useClerkWorkspace();

  useEffect(() => {
    if (currentWorkspace) {
      loadDocuments();
    }
  }, [currentWorkspace, folderId, typeFilter, statusFilter]);

  const loadDocuments = async () => {
    if (!currentWorkspace) return;

    setIsLoading(true);
    try {
      const token = await getToken();
      
      // Load contracts
      const contractsResponse = await ContractService.getContracts(
        { workspace_id: currentWorkspace.id },
        token || undefined
      );

      // Load documents (placeholder for future implementation)

      const allDocuments: DocumentItem[] = [];

      // Add contracts
      if (contractsResponse.data) {
        contractsResponse.data.forEach(contract => {
          allDocuments.push({
            id: contract.id,
            title: contract.title,
            type: 'contract',
            format: 'Contract',
            size: 0, // Contracts don't have file size
            created_at: contract.created_at,
            created_by: contract.created_by.name,
            folder_id: contract.folder_id,
            tags: contract.tags,
            status: contract.status,
          });
        });
      }

      // Add documents (placeholder - would be implemented with real document service)
      // if (documentsResponse.success && documentsResponse.data) {
      //   documentsResponse.data.forEach(doc => {
      //     allDocuments.push({
      //       id: doc.id,
      //       title: doc.title,
      //       type: 'document',
      //       format: doc.filename?.split('.').pop()?.toUpperCase() || 'Unknown',
      //       size: doc.size || 0,
      //       created_at: doc.created_at,
      //       created_by: doc.created_by?.name || 'Unknown',
      //       folder_id: doc.folder_id,
      //       tags: doc.tags,
      //     });
      //   });
      // }

      // Apply filters
      let filteredDocuments = allDocuments;
      
      if (typeFilter !== 'all') {
        filteredDocuments = filteredDocuments.filter(doc => doc.type === typeFilter);
      }
      
      if (statusFilter !== 'all' && statusFilter) {
        filteredDocuments = filteredDocuments.filter(doc => doc.status === statusFilter);
      }
      
      if (searchQuery) {
        filteredDocuments = filteredDocuments.filter(doc =>
          doc.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
          doc.created_by.toLowerCase().includes(searchQuery.toLowerCase())
        );
      }

      setDocuments(filteredDocuments);
    } catch (error) {
      console.error('Failed to load documents:', error);
      toast({
        title: 'Error',
        description: 'Failed to load documents',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSelectDocument = (documentId: string) => {
    setSelectedDocuments(prev =>
      prev.includes(documentId)
        ? prev.filter(id => id !== documentId)
        : [...prev, documentId]
    );
  };

  const handleSelectAll = () => {
    if (selectedDocuments.length === documents.length) {
      setSelectedDocuments([]);
    } else {
      setSelectedDocuments(documents.map(doc => doc.id));
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return 'N/A';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'contract':
        return <FileText className="h-4 w-4 text-blue-500" />;
      case 'template':
        return <FileText className="h-4 w-4 text-green-500" />;
      default:
        return <FileText className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status?: string) => {
    if (!status) return null;
    
    const statusColors = {
      draft: 'bg-gray-100 text-gray-800',
      active: 'bg-green-100 text-green-800',
      expired: 'bg-red-100 text-red-800',
      pending: 'bg-yellow-100 text-yellow-800',
    };

    return (
      <Badge className={statusColors[status as keyof typeof statusColors] || 'bg-gray-100 text-gray-800'}>
        {status}
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Document Export Manager</h2>
          <p className="text-muted-foreground">
            Export contracts and documents in multiple formats
          </p>
        </div>
        <Button onClick={loadDocuments} variant="outline" size="sm">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Filters */}
      {showFilters && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Filters & Search</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="space-y-2">
                <Label htmlFor="search">Search</Label>
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="search"
                    placeholder="Search documents..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-8"
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label>Document Type</Label>
                <Select value={typeFilter} onValueChange={setTypeFilter}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value="contract">Contracts</SelectItem>
                    <SelectItem value="document">Documents</SelectItem>
                    <SelectItem value="template">Templates</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label>Status</Label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="expired">Expired</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label>Quick Actions</Label>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleSelectAll}
                    className="flex-1"
                  >
                    {selectedDocuments.length === documents.length ? (
                      <CheckSquare className="h-4 w-4 mr-1" />
                    ) : (
                      <Square className="h-4 w-4 mr-1" />
                    )}
                    Select All
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Bulk Actions */}
      {selectedDocuments.length > 0 && (
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">
                {selectedDocuments.length} document{selectedDocuments.length !== 1 ? 's' : ''} selected
              </span>
              <div className="flex gap-2">
                <BatchExportButton
                  contractIds={selectedDocuments.filter(id => 
                    documents.find(doc => doc.id === id)?.type === 'contract'
                  )}
                  contractTitles={selectedDocuments
                    .filter(id => documents.find(doc => doc.id === id)?.type === 'contract')
                    .map(id => documents.find(doc => doc.id === id)?.title || 'Unknown')
                  }
                  size="sm"
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setSelectedDocuments([])}
                >
                  Clear Selection
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Documents Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FolderOpen className="h-5 w-5" />
            Documents ({documents.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-8">
              <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
              <p>Loading documents...</p>
            </div>
          ) : documents.length === 0 ? (
            <div className="text-center py-8">
              <FileText className="h-8 w-8 mx-auto mb-4 text-muted-foreground" />
              <p className="text-muted-foreground">No documents found</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-12">
                    <button onClick={handleSelectAll}>
                      {selectedDocuments.length === documents.length ? (
                        <CheckSquare className="h-4 w-4" />
                      ) : (
                        <Square className="h-4 w-4" />
                      )}
                    </button>
                  </TableHead>
                  <TableHead>Document</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Format</TableHead>
                  <TableHead>Size</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {documents.map((document) => (
                  <TableRow key={document.id}>
                    <TableCell>
                      <button onClick={() => handleSelectDocument(document.id)}>
                        {selectedDocuments.includes(document.id) ? (
                          <CheckSquare className="h-4 w-4" />
                        ) : (
                          <Square className="h-4 w-4" />
                        )}
                      </button>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getTypeIcon(document.type)}
                        <div>
                          <div className="font-medium">{document.title}</div>
                          <div className="text-sm text-muted-foreground">
                            by {document.created_by}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">{document.type}</Badge>
                    </TableCell>
                    <TableCell>{document.format}</TableCell>
                    <TableCell>{formatFileSize(document.size)}</TableCell>
                    <TableCell>{formatDate(document.created_at)}</TableCell>
                    <TableCell>{getStatusBadge(document.status)}</TableCell>
                    <TableCell>
                      {document.type === 'contract' && (
                        <DocumentExportButton
                          contractId={document.id}
                          contractTitle={document.title}
                          variant="ghost"
                          size="sm"
                          showLabel={false}
                          enableTemplateSelection={true}
                          workspaceId={currentWorkspace?.id}
                        />
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default DocumentExportManager;
