import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@/components/ui/avatar";
import {
  Check,
  Download,
  Eye,
  FileEdit,
  FileText,
  History,
  Pencil,
  Search,
  Trash,
  Upload,
  User,
  UserPlus,
  X,
} from "lucide-react";

// Types
interface ActivityItem {
  id: string;
  type: "create" | "update" | "delete" | "approve" | "reject" | "share" | "upload" | "download" | "comment" | "user";
  entityType: "contract" | "template" | "clause" | "user" | "role" | "approval" | "comment";
  entityId: string;
  entityName: string;
  user: {
    id: string;
    name: string;
    avatar?: string;
    initials: string;
  };
  timestamp: string;
  details?: string;
  metadata?: Record<string, unknown>;
}

interface CompactActivityHistoryProps {
  activities?: ActivityItem[];
  onViewEntity?: (entityType: string, entityId: string) => void;
}

const CompactActivityHistory: React.FC<CompactActivityHistoryProps> = ({
  activities = [],
  onViewEntity,
}) => {
  // State
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [activeTab] = useState<string>("all");
  const [selectedUser, setSelectedUser] = useState<string>("all");
  const [selectedEntityType] = useState<string>("all");
  const [selectedActionType] = useState<string>("all");

  // Filter activities based on filters
  const filteredActivities = activities.filter(activity => {
    // Filter by search term
    const matchesSearch =
      searchTerm === "" ||
      activity.entityName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      activity.user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (activity.details && activity.details.toLowerCase().includes(searchTerm.toLowerCase()));

    // Filter by tab
    const matchesTab =
      activeTab === "all" ||
      (activeTab === "contracts" && activity.entityType === "contract") ||
      (activeTab === "templates" && activity.entityType === "template") ||
      (activeTab === "approvals" && (activity.type === "approve" || activity.type === "reject"));

    // Filter by user
    const matchesUser =
      selectedUser === "all" ||
      activity.user.id === selectedUser;

    // Filter by entity type
    const matchesEntityType =
      selectedEntityType === "all" ||
      activity.entityType === selectedEntityType;

    // Filter by action type
    const matchesActionType =
      selectedActionType === "all" ||
      activity.type === selectedActionType;

    // Filter by date range
    const matchesDateRange = true; // Implement date range filtering if needed

    return matchesSearch && matchesTab && matchesUser && matchesEntityType && matchesActionType && matchesDateRange;
  });

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
    const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

    if (diffInMinutes < 60) {
      return `${diffInMinutes} ${diffInMinutes === 1 ? "minute" : "minutes"} ago`;
    } else if (diffInHours < 24) {
      return `${diffInHours} ${diffInHours === 1 ? "hour" : "hours"} ago`;
    } else if (diffInDays < 7) {
      return `${diffInDays} ${diffInDays === 1 ? "day" : "days"} ago`;
    } else {
      return date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
      });
    }
  };

  // Get activity icon
  const getActivityIcon = (type: ActivityItem["type"]) => {
    switch (type) {
      case "create":
        return <FileText className="h-3 w-3 text-green-500" />;
      case "update":
        return <FileEdit className="h-3 w-3 text-blue-500" />;
      case "delete":
        return <Trash className="h-3 w-3 text-red-500" />;
      case "approve":
        return <Check className="h-3 w-3 text-green-500" />;
      case "reject":
        return <X className="h-3 w-3 text-red-500" />;
      case "share":
        return <User className="h-3 w-3 text-purple-500" />;
      case "upload":
        return <Upload className="h-3 w-3 text-blue-500" />;
      case "download":
        return <Download className="h-3 w-3 text-blue-500" />;
      case "comment":
        return <Pencil className="h-3 w-3 text-amber-500" />;
      case "user":
        return <UserPlus className="h-3 w-3 text-green-500" />;
      default:
        return <History className="h-3 w-3 text-muted-foreground" />;
    }
  };

  // Get unique users from activities
  const uniqueUsers = Array.from(
    new Set(activities.map(activity => activity.user.id))
  ).map(userId => {
    const activity = activities.find(a => a.user.id === userId);
    return {
      id: userId,
      name: activity?.user.name || "",
      avatar: activity?.user.avatar,
      initials: activity?.user.initials || "",
    };
  });

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <div className="relative w-full max-w-[200px]">
          <Search className="absolute left-2 top-1.5 h-3 w-3 text-muted-foreground" />
          <Input
            placeholder="Search activities..."
            className="pl-7 h-7 text-xs"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <Select value={selectedUser} onValueChange={setSelectedUser}>
          <SelectTrigger className="w-[120px] h-7 text-xs">
            <SelectValue placeholder="Filter by user" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Users</SelectItem>
            {uniqueUsers.map(user => (
              <SelectItem key={user.id} value={user.id}>
                <div className="flex items-center gap-1">
                  <Avatar className="h-4 w-4">
                    {user.avatar ? (
                      <AvatarImage src={user.avatar} alt={user.name} />
                    ) : (
                      <AvatarFallback className="text-[8px]">
                        {user.initials}
                      </AvatarFallback>
                    )}
                  </Avatar>
                  <span className="text-xs">{user.name}</span>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <ScrollArea className="h-[400px]">
        <div className="space-y-1">
          {filteredActivities.length > 0 ? (
            filteredActivities.map((activity) => (
              <div
                key={activity.id}
                className="py-2 px-3 hover:bg-muted/50 rounded-md"
              >
                <div className="flex items-center gap-2">
                  <Avatar className="h-6 w-6">
                    {activity.user.avatar ? (
                      <AvatarImage src={activity.user.avatar} alt={activity.user.name} />
                    ) : (
                      <AvatarFallback className="text-[10px]">
                        {activity.user.initials}
                      </AvatarFallback>
                    )}
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-baseline">
                      <span className="font-medium text-xs mr-1">{activity.user.name}</span>
                      <span className="text-xs text-muted-foreground truncate">
                        {activity.type === "create" && "created contract"}
                        {activity.type === "update" && "updated contract"}
                        {activity.type === "approve" && "approved contract"}
                        {activity.type === "share" && "shared contract"}
                        {activity.type === "comment" && "commented on contract"}
                      </span>
                    </div>
                    <div className="text-xs font-medium truncate">
                      &quot;{activity.entityName}&quot;
                    </div>
                    {activity.details && (
                      <p className="text-xs text-muted-foreground truncate">
                        {activity.details}
                      </p>
                    )}
                  </div>
                  <div className="flex flex-col items-end gap-1">
                    <div className="text-[10px] text-muted-foreground whitespace-nowrap">
                      {formatDate(activity.timestamp)}
                    </div>
                    <div className="flex items-center gap-1">
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-5 w-5"
                        onClick={() => onViewEntity && onViewEntity(activity.entityType, activity.entityId)}
                      >
                        <Eye className="h-3 w-3" />
                      </Button>
                      <Badge variant="outline" className="text-[10px] h-5 px-1 flex items-center gap-1">
                        {getActivityIcon(activity.type)}
                        <span className="capitalize">{activity.type}</span>
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="flex flex-col items-center justify-center py-8 text-center">
              <History className="h-8 w-8 text-muted-foreground mb-2" />
              <h3 className="text-sm font-medium mb-1">No activities found</h3>
              <p className="text-xs text-muted-foreground">
                {searchTerm || selectedUser !== "all"
                  ? "Try adjusting your filters"
                  : "No activities have been recorded yet"}
              </p>
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  );
};

export type { ActivityItem };
export default CompactActivityHistory;
