import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@/components/ui/avatar";
import {
  Clock,
  Eye,
  FileEdit,
  FileText,
  History,
  Pencil,
  Search,
  Check,
  Trash,
  Upload,
  Download,
  User,
  UserPlus,
  X,
} from "lucide-react";

// Types
interface ActivityItem {
  id: string;
  type: "create" | "update" | "delete" | "approve" | "reject" | "share" | "upload" | "download" | "comment" | "user";
  entityType: "contract" | "template" | "clause" | "user" | "role" | "approval" | "comment";
  entityId: string;
  entityName: string;
  user: {
    id: string;
    name: string;
    avatar?: string;
    initials: string;
  };
  timestamp: string;
  details?: string;
  metadata?: Record<string, unknown>;
}

interface ModerateActivityHistoryProps {
  activities?: ActivityItem[];
  onViewEntity?: (entityType: string, entityId: string) => void;
}

const ModerateActivityHistory: React.FC<ModerateActivityHistoryProps> = ({
  activities = [],
  onViewEntity,
}) => {
  // State
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [selectedUser, setSelectedUser] = useState<string>("all");

  // Filter activities based on filters
  const filteredActivities = activities.filter(activity => {
    // Filter by search term
    const matchesSearch =
      searchTerm === "" ||
      activity.entityName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      activity.user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (activity.details && activity.details.toLowerCase().includes(searchTerm.toLowerCase()));

    // Filter by user
    const matchesUser =
      selectedUser === "all" ||
      activity.user.id === selectedUser;

    return matchesSearch && matchesUser;
  });

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
    const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

    if (diffInMinutes < 60) {
      return `${diffInMinutes} ${diffInMinutes === 1 ? "minute" : "minutes"} ago`;
    } else if (diffInHours < 24) {
      return `${diffInHours} ${diffInHours === 1 ? "hour" : "hours"} ago`;
    } else if (diffInDays < 7) {
      return `${diffInDays} ${diffInDays === 1 ? "day" : "days"} ago`;
    } else {
      return date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
      });
    }
  };

  // Get activity icon
  const getActivityIcon = (type: ActivityItem["type"]) => {
    switch (type) {
      case "create":
        return <FileText className="h-4 w-4 text-green-500" />;
      case "update":
        return <FileEdit className="h-4 w-4 text-blue-500" />;
      case "delete":
        return <Trash className="h-4 w-4 text-red-500" />;
      case "approve":
        return <Check className="h-4 w-4 text-green-500" />;
      case "reject":
        return <X className="h-4 w-4 text-red-500" />;
      case "share":
        return <User className="h-4 w-4 text-purple-500" />;
      case "upload":
        return <Upload className="h-4 w-4 text-blue-500" />;
      case "download":
        return <Download className="h-4 w-4 text-blue-500" />;
      case "comment":
        return <Pencil className="h-4 w-4 text-amber-500" />;
      case "user":
        return <UserPlus className="h-4 w-4 text-green-500" />;
      default:
        return <History className="h-4 w-4 text-muted-foreground" />;
    }
  };

  // Get unique users from activities
  const uniqueUsers = Array.from(
    new Set(activities.map(activity => activity.user.id))
  ).map(userId => {
    const activity = activities.find(a => a.user.id === userId);
    return {
      id: userId,
      name: activity?.user.name || "",
      avatar: activity?.user.avatar,
      initials: activity?.user.initials || "",
    };
  });

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="relative w-full max-w-[250px]">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search activities..."
            className="pl-8 h-9"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <Select value={selectedUser} onValueChange={setSelectedUser}>
          <SelectTrigger className="w-[150px] h-9">
            <SelectValue placeholder="Filter by user" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Users</SelectItem>
            {uniqueUsers.map(user => (
              <SelectItem key={user.id} value={user.id}>
                <div className="flex items-center gap-2">
                  <Avatar className="h-5 w-5">
                    {user.avatar ? (
                      <AvatarImage src={user.avatar} alt={user.name} />
                    ) : (
                      <AvatarFallback className="text-xs">
                        {user.initials}
                      </AvatarFallback>
                    )}
                  </Avatar>
                  <span>{user.name}</span>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <ScrollArea className="h-[400px]">
        <div className="space-y-2">
          {filteredActivities.length > 0 ? (
            filteredActivities.map((activity) => (
              <div
                key={activity.id}
                className="py-3 px-4 hover:bg-muted/50 rounded-md border border-border/40"
              >
                <div className="flex items-center gap-3">
                  <Avatar className="h-8 w-8">
                    {activity.user.avatar ? (
                      <AvatarImage src={activity.user.avatar} alt={activity.user.name} />
                    ) : (
                      <AvatarFallback>
                        {activity.user.initials}
                      </AvatarFallback>
                    )}
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-baseline">
                      <span className="font-medium text-sm mr-1">{activity.user.name}</span>
                      <span className="text-sm text-muted-foreground">
                        {activity.type === "create" && "created contract"}
                        {activity.type === "update" && "updated contract"}
                        {activity.type === "approve" && "approved contract"}
                        {activity.type === "share" && "shared contract"}
                        {activity.type === "comment" && "commented on contract"}
                      </span>
                    </div>
                    <div className="text-sm font-medium">
                      &quot;{activity.entityName}&quot;
                    </div>
                    {activity.details && (
                      <p className="text-sm text-muted-foreground">
                        {activity.details}
                      </p>
                    )}
                  </div>
                  <div className="flex flex-col items-end gap-2">
                    <div className="text-xs text-muted-foreground flex items-center">
                      <Clock className="h-3 w-3 mr-1" />
                      {formatDate(activity.timestamp)}
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-7 w-7"
                        onClick={() => onViewEntity && onViewEntity(activity.entityType, activity.entityId)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Badge variant="outline" className="text-xs px-2 py-0 h-6 flex items-center gap-1">
                        {getActivityIcon(activity.type)}
                        <span className="capitalize">{activity.type}</span>
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <History className="h-10 w-10 text-muted-foreground mb-3" />
              <h3 className="text-base font-medium mb-1">No activities found</h3>
              <p className="text-sm text-muted-foreground">
                {searchTerm || selectedUser !== "all"
                  ? "Try adjusting your filters"
                  : "No activities have been recorded yet"}
              </p>
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  );
};

export type { ActivityItem };
export default ModerateActivityHistory;
