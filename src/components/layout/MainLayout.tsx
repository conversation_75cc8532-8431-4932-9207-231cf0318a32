import React, { useState, useEffect } from "react";
import { Outlet, useLocation } from "react-router-dom";
import Header from "./Header";
import Sidebar from "./Sidebar";
import ThemeToggle from "../theme/ThemeToggle";
import PermissionCheck from "../permissions/PermissionCheck";
import { Toaster } from "@/components/ui/toaster";
import { WorkspaceProvider, useWorkspace, Workspace } from "@/lib/workspace-provider";
import { ClerkWorkspaceProvider, useClerkWorkspace } from "@/lib/clerk-workspace-provider";
import { LiveRegion } from "@/components/ui/a11y";
import { useError } from "@/lib/error-provider";
import NoWorkspacesScreen from "../workspace/NoWorkspacesScreen";

const MainLayoutContent = () => {
  const location = useLocation();
  // Use Clerk workspace provider instead of the mock one
  const { currentWorkspace, setCurrentWorkspace, currentUser, isLoading, userWorkspaces, getUserWorkspaces } = useClerkWorkspace();
  const { error } = useError();
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [isWorkspaceManagerOpen, setIsWorkspaceManagerOpen] = useState(false);
  const [pageTitle, setPageTitle] = useState("");

  // Handlers
  const handleToggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  const handleWorkspaceChange = (workspace: Workspace) => {
    setCurrentWorkspace(workspace);
  };

  const handleOpenWorkspaceManager = () => {
    setIsWorkspaceManagerOpen(true);
  };

  // Update page title based on location
  useEffect(() => {
    // Map routes to page titles
    const routeTitles: Record<string, string> = {
      '/dashboard': 'Dashboard',
      '/contracts': 'Contracts',
      '/contracts/wizard': 'Contract Wizard',
      '/contracts/templates': 'Contract Templates',
      '/repository': 'Document Repository',
      '/approvals': 'Approvals',
      '/analytics': 'Analytics',
      '/settings': 'Settings',
    };

    // Find the matching route or use a default
    const matchingRoute = Object.keys(routeTitles).find(route =>
      location.pathname.startsWith(route)
    );

    const newTitle = matchingRoute ? routeTitles[matchingRoute] : 'LegalAI';
    setPageTitle(newTitle);

    // Update document title for browser tab
    document.title = `${newTitle} | LegalAI`;
  }, [location.pathname]);

  // Show loading state while Clerk is initializing
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Show NoWorkspacesScreen if user has no workspaces and is not on workspace management pages
  const userWorkspacesList = getUserWorkspaces();
  const isOnWorkspacePages = location.pathname.startsWith('/workspaces') || location.pathname.startsWith('/test');

  // Only show NoWorkspacesScreen if we're absolutely sure the user has no workspaces
  // We need to be very careful here to avoid showing this screen when data is still loading
  // or when the user actually has workspaces
  const shouldShowNoWorkspacesScreen = !isLoading &&
                                       currentUser &&
                                       userWorkspacesList.length === 0 &&
                                       userWorkspaces.length === 0 &&
                                       !isOnWorkspacePages;

  // For now, let's disable the NoWorkspacesScreen to ensure users with workspaces can access the app
  // This can be re-enabled once we're sure the workspace detection is working correctly
  const DISABLE_NO_WORKSPACES_SCREEN = true;

  if (shouldShowNoWorkspacesScreen && !DISABLE_NO_WORKSPACES_SCREEN) {
    return <NoWorkspacesScreen autoRedirect={false} />;
  }

  return (
    <div className="flex h-screen w-full bg-background overflow-hidden">
      {/* Accessibility announcements */}
      <LiveRegion aria-live="polite">
        {error && `Error: ${error.message}`}
        {pageTitle && `Current page: ${pageTitle}`}
      </LiveRegion>

      {/* Mobile sidebar overlay */}
      {isSidebarOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-40 md:hidden"
          onClick={handleToggleSidebar}
          aria-hidden="true"
        />
      )}

      {/* Sidebar */}
      <div
        className={`fixed md:relative z-50 md:z-auto transition-transform duration-300 transform ${isSidebarOpen ? "translate-x-0" : "-translate-x-full"} md:translate-x-0 h-full`}
        style={{
          paddingTop: 'env(safe-area-inset-top)',
          paddingLeft: 'env(safe-area-inset-left)'
        }}
        role="navigation"
        aria-label="Main navigation"
      >
        <Sidebar
          activePath={location.pathname}
          userName={currentUser?.name || ""}
          userRole={currentUser?.role || "Member"}
          currentWorkspace={currentWorkspace || undefined}
          onWorkspaceChange={handleWorkspaceChange}
        />
      </div>

      {/* Main content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <Header
          currentWorkspace={currentWorkspace || undefined}
          onToggleSidebar={handleToggleSidebar}
          onWorkspaceChange={handleWorkspaceChange}
          pageTitle={pageTitle}
        />

        <main id="main-content" className="flex-1 overflow-auto" tabIndex={-1}>
          <div className="min-h-full pb-safe" style={{
            paddingBottom: 'env(safe-area-inset-bottom)',
            paddingRight: 'env(safe-area-inset-right)'
          }}>
            <PermissionCheck>
              <Outlet />
            </PermissionCheck>
          </div>
        </main>
      </div>

      {/* Theme toggle for mobile (fixed position) */}
      <div className="md:hidden fixed bottom-4 right-4 z-50" style={{
        bottom: 'calc(1rem + env(safe-area-inset-bottom))',
        right: 'calc(1rem + env(safe-area-inset-right))'
      }}>
        <ThemeToggle variant="outline" size="default" />
      </div>

      {/* Toast notifications */}
      <Toaster />
    </div>
  );
};

const MainLayout = () => {
  return (
    <ClerkWorkspaceProvider>
      <MainLayoutContent />
    </ClerkWorkspaceProvider>
  );
};

export default MainLayout;
