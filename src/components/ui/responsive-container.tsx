import React, { ReactNode } from "react";
import { cn } from "@/lib/utils";

type ContainerSize = "xs" | "sm" | "md" | "lg" | "xl" | "full";

interface ResponsiveContainerProps {
  children: ReactNode;
  size?: ContainerSize;
  className?: string;
  as?: React.ElementType;
  padding?: boolean;
}

const sizeClasses: Record<ContainerSize, string> = {
  xs: "max-w-xs",
  sm: "max-w-sm",
  md: "max-w-md",
  lg: "max-w-lg",
  xl: "max-w-xl",
  full: "max-w-full",
};

export const ResponsiveContainer: React.FC<ResponsiveContainerProps> = ({
  children,
  size = "lg",
  className,
  as: Component = "div",
  padding = true,
}) => {
  return (
    <Component
      className={cn(
        "w-full mx-auto",
        sizeClasses[size],
        padding && "px-4 sm:px-6 md:px-8",
        className
      )}
    >
      {children}
    </Component>
  );
};

interface ResponsiveGridProps {
  children: ReactNode;
  columns?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
  gap?: string;
  className?: string;
}

export const ResponsiveGrid: React.FC<ResponsiveGridProps> = ({
  children,
  columns = {
    xs: 1,
    sm: 2,
    md: 3,
    lg: 4,
    xl: 4,
  },
  gap = "gap-4",
  className,
}) => {
  const gridClasses = [
    "grid",
    gap,
    columns.xs && `grid-cols-${columns.xs}`,
    columns.sm && `sm:grid-cols-${columns.sm}`,
    columns.md && `md:grid-cols-${columns.md}`,
    columns.lg && `lg:grid-cols-${columns.lg}`,
    columns.xl && `xl:grid-cols-${columns.xl}`,
  ]
    .filter(Boolean)
    .join(" ");

  return <div className={cn(gridClasses, className)}>{children}</div>;
};

interface StackProps {
  children: ReactNode;
  direction?: "row" | "column";
  spacing?: "none" | "xs" | "sm" | "md" | "lg" | "xl";
  wrap?: boolean;
  align?: "start" | "center" | "end" | "stretch" | "baseline";
  justify?: "start" | "center" | "end" | "between" | "around" | "evenly";
  className?: string;
  responsive?: boolean;
}

const spacingClasses: Record<Exclude<StackProps["spacing"], "none">, string> = {
  xs: "gap-1",
  sm: "gap-2",
  md: "gap-4",
  lg: "gap-6",
  xl: "gap-8",
};

export const Stack: React.FC<StackProps> = ({
  children,
  direction = "column",
  spacing = "md",
  wrap = false,
  align,
  justify,
  className,
  responsive = false,
}) => {
  const alignmentClasses = {
    row: {
      start: "items-start",
      center: "items-center",
      end: "items-end",
      stretch: "items-stretch",
      baseline: "items-baseline",
    },
    column: {
      start: "items-start",
      center: "items-center",
      end: "items-end",
      stretch: "items-stretch",
      baseline: "items-baseline",
    },
  };

  const justifyClasses = {
    start: "justify-start",
    center: "justify-center",
    end: "justify-end",
    between: "justify-between",
    around: "justify-around",
    evenly: "justify-evenly",
  };

  const classes = [
    direction === "row" ? "flex flex-row" : "flex flex-col",
    responsive && direction === "row" ? "flex-col md:flex-row" : "",
    responsive && direction === "column" ? "flex-col" : "",
    wrap && "flex-wrap",
    spacing !== "none" && spacingClasses[spacing],
    align && alignmentClasses[direction][align],
    justify && justifyClasses[justify],
  ]
    .filter(Boolean)
    .join(" ");

  return <div className={cn(classes, className)}>{children}</div>;
};
