import React, { useState, useEffect } from "react";
import { cn } from "@/lib/utils";
import { Keyboard, X } from "lucide-react";
import { Button } from "./button";

interface KeyboardShortcutsProps {
  className?: string;
}

const KeyboardShortcuts: React.FC<KeyboardShortcutsProps> = ({ className }) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Show shortcuts on Ctrl/Cmd + ?
      if ((e.ctrlKey || e.metaKey) && e.key === "/") {
        e.preventDefault();
        setIsVisible(true);
      }
      // Hide on Escape
      if (e.key === "Escape") {
        setIsVisible(false);
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, []);

  if (!isVisible) {
    return (
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setIsVisible(true)}
        className={cn("fixed bottom-4 left-4 z-50 opacity-50 hover:opacity-100", className)}
        title="Keyboard shortcuts (Ctrl+/)"
      >
        <Keyboard className="h-4 w-4" />
      </Button>
    );
  }

  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
      <div className="bg-background border rounded-lg shadow-lg max-w-md w-full p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">Keyboard Shortcuts</h3>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setIsVisible(false)}
            className="h-6 w-6"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
        
        <div className="space-y-3 text-sm">
          <div className="space-y-2">
            <h4 className="font-medium text-muted-foreground">Table Navigation</h4>
            <div className="grid grid-cols-2 gap-2">
              <div className="flex justify-between">
                <span>Navigate rows</span>
                <kbd className="px-2 py-1 bg-muted rounded text-xs">↑ ↓</kbd>
              </div>
              <div className="flex justify-between">
                <span>Select row</span>
                <kbd className="px-2 py-1 bg-muted rounded text-xs">Enter</kbd>
              </div>
              <div className="flex justify-between">
                <span>Exit focus</span>
                <kbd className="px-2 py-1 bg-muted rounded text-xs">Esc</kbd>
              </div>
            </div>
          </div>
          
          <div className="space-y-2">
            <h4 className="font-medium text-muted-foreground">General</h4>
            <div className="grid grid-cols-1 gap-2">
              <div className="flex justify-between">
                <span>Show shortcuts</span>
                <kbd className="px-2 py-1 bg-muted rounded text-xs">Ctrl + /</kbd>
              </div>
              <div className="flex justify-between">
                <span>Search</span>
                <kbd className="px-2 py-1 bg-muted rounded text-xs">Ctrl + K</kbd>
              </div>
            </div>
          </div>
        </div>
        
        <div className="mt-4 pt-4 border-t text-xs text-muted-foreground">
          Press <kbd className="px-1 py-0.5 bg-muted rounded">Esc</kbd> or click outside to close
        </div>
      </div>
    </div>
  );
};

export default KeyboardShortcuts;
