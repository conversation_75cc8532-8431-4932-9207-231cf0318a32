import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/components/ui/use-toast';
import { ContractService } from '@/services/api-services';
import { useAuth } from '@clerk/clerk-react';
import {
  Download,
  FileText,
  File,
  Globe,
  Code,
  Loader2,
  CheckCircle,
  AlertCircle,
  Package,
} from 'lucide-react';

interface BatchExportButtonProps {
  contractIds: string[];
  contractTitles?: string[];
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'default' | 'sm' | 'lg';
  className?: string;
  disabled?: boolean;
}

type ExportFormat = 'pdf' | 'docx' | 'html' | 'txt' | 'markdown';

interface ExportOption {
  format: ExportFormat;
  label: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
}

const exportOptions: ExportOption[] = [
  {
    format: 'pdf',
    label: 'PDF Documents',
    description: 'Professional PDFs with formatting',
    icon: FileText,
  },
  {
    format: 'docx',
    label: 'Word Documents',
    description: 'Editable Microsoft Word format',
    icon: File,
  },
  {
    format: 'html',
    label: 'HTML Documents',
    description: 'Web-ready HTML format',
    icon: Globe,
  },
  {
    format: 'txt',
    label: 'Plain Text Files',
    description: 'Simple text format',
    icon: FileText,
  },
  {
    format: 'markdown',
    label: 'Markdown Files',
    description: 'Markdown format for documentation',
    icon: Code,
  },
];

const BatchExportButton: React.FC<BatchExportButtonProps> = ({
  contractIds,
  contractTitles = [],
  variant = 'outline',
  size = 'default',
  className = '',
  disabled = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedFormat, setSelectedFormat] = useState<ExportFormat>('pdf');
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const [exportResults, setExportResults] = useState<{
    total: number;
    successful: number;
    failed: number;
    results: Array<{
      contract_id: string;
      success: boolean;
      filename?: string;
      url?: string;
      error?: string;
    }>;
  } | null>(null);
  const { toast } = useToast();
  const { getToken } = useAuth();

  const handleExport = async () => {
    if (contractIds.length === 0) return;

    setIsExporting(true);
    setExportProgress(0);
    setExportResults(null);

    try {
      // Get auth token
      const token = await getToken();

      // Start batch export
      const response = await ContractService.batchGenerateDocuments(
        contractIds,
        selectedFormat,
        token || undefined
      );

      if (response.success && response.data) {
        const { results, summary } = response.data;
        
        setExportResults({
          total: summary.total,
          successful: summary.successful,
          failed: summary.failed,
          results: results.map(result => ({
            contract_id: result.contract_id,
            success: result.success,
            filename: result.file_info?.filename,
            url: result.file_info?.url,
            error: result.error,
          })),
        });

        setExportProgress(100);

        // Download successful files
        const successfulResults = results.filter(r => r.success && r.file_info);
        
        if (successfulResults.length > 0) {
          // Create download links for each successful export
          successfulResults.forEach((result, index) => {
            if (result.file_info) {
              setTimeout(() => {
                const link = document.createElement('a');
                link.href = result.file_info!.url;
                link.download = result.file_info!.filename;
                link.target = '_blank';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
              }, index * 500); // Stagger downloads by 500ms
            }
          });
        }

        toast({
          title: 'Batch Export Complete',
          description: `${summary.successful} of ${summary.total} contracts exported successfully`,
          variant: summary.failed > 0 ? 'default' : 'default',
        });
      } else {
        throw new Error(response.message || 'Batch export failed');
      }
    } catch (error: any) {
      console.error('Batch export error:', error);
      
      toast({
        title: 'Batch Export Failed',
        description: error.message || 'Failed to export contracts',
        variant: 'destructive',
      });
    } finally {
      setIsExporting(false);
    }
  };

  const handleClose = () => {
    if (!isExporting) {
      setIsOpen(false);
      setExportResults(null);
      setExportProgress(0);
    }
  };

  const selectedOption = exportOptions.find(opt => opt.format === selectedFormat);

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button 
          variant={variant} 
          size={size} 
          className={className}
          disabled={disabled || contractIds.length === 0}
        >
          <Package className="h-4 w-4" />
          <span className="ml-2">
            Batch Export ({contractIds.length})
          </span>
        </Button>
      </DialogTrigger>
      
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Batch Export Contracts
          </DialogTitle>
          <DialogDescription>
            Export {contractIds.length} selected contract{contractIds.length !== 1 ? 's' : ''} in your preferred format.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Format Selection */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Export Format</label>
            <Select 
              value={selectedFormat} 
              onValueChange={(value: ExportFormat) => setSelectedFormat(value)}
              disabled={isExporting}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {exportOptions.map((option) => {
                  const Icon = option.icon;
                  return (
                    <SelectItem key={option.format} value={option.format}>
                      <div className="flex items-center gap-2">
                        <Icon className="h-4 w-4" />
                        <div>
                          <div className="font-medium">{option.label}</div>
                          <div className="text-xs text-muted-foreground">
                            {option.description}
                          </div>
                        </div>
                      </div>
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          </div>

          {/* Progress */}
          {isExporting && (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Exporting contracts...</span>
                <span>{exportProgress}%</span>
              </div>
              <Progress value={exportProgress} className="h-2" />
            </div>
          )}

          {/* Results */}
          {exportResults && (
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                <span className="font-medium">Export Complete</span>
              </div>
              
              <div className="grid grid-cols-3 gap-2 text-center">
                <div className="p-2 bg-muted rounded">
                  <div className="text-lg font-bold">{exportResults.total}</div>
                  <div className="text-xs text-muted-foreground">Total</div>
                </div>
                <div className="p-2 bg-green-50 rounded">
                  <div className="text-lg font-bold text-green-600">{exportResults.successful}</div>
                  <div className="text-xs text-muted-foreground">Success</div>
                </div>
                <div className="p-2 bg-red-50 rounded">
                  <div className="text-lg font-bold text-red-600">{exportResults.failed}</div>
                  <div className="text-xs text-muted-foreground">Failed</div>
                </div>
              </div>

              {exportResults.failed > 0 && (
                <div className="text-xs text-muted-foreground">
                  Failed exports may be due to missing data or permissions.
                </div>
              )}
            </div>
          )}
        </div>

        <DialogFooter>
          <Button 
            variant="outline" 
            onClick={handleClose}
            disabled={isExporting}
          >
            {exportResults ? 'Close' : 'Cancel'}
          </Button>
          {!exportResults && (
            <Button 
              onClick={handleExport}
              disabled={isExporting || contractIds.length === 0}
            >
              {isExporting ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Exporting...
                </>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-2" />
                  Export {contractIds.length} Contract{contractIds.length !== 1 ? 's' : ''}
                </>
              )}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default BatchExportButton;
