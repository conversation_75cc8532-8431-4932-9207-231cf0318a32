import { useState, useEffect } from "react";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import EnhancedTable, { type ColumnDef } from "@/components/ui/enhanced-table";
import { useTableState } from "@/hooks/useTableState";

import {
  Eye,
  FileText,
  MoreHorizontal,
  Download,
  Copy,
  Trash2,
  FileEdit,
  ExternalLink,
  Mail,
  Loader2
} from "lucide-react";
import { useWorkspace } from "@/lib/workspace-provider";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { toast } from "@/components/ui/use-toast";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>it<PERSON>,
} from "@/components/ui/dialog";
import { useApi } from "@/lib/api";
import { ContractService } from "@/services/api-services";
import type { Contract as ApiContract } from "@/services/api-types";
import AdvancedDocumentGenerator from "./AdvancedDocumentGenerator";

// Local interface for contracts with UI-specific properties
interface Contract {
  id: string;
  title: string;
  type: string;
  status: "draft" | "review" | "active" | "expired" | "rejected" | "pending_approval" | "terminated";
  createdBy: {
    name: string;
    avatar?: string;
    initials: string;
    id?: string;
  };
  createdDate: string;
  expiryDate?: string;
  counterparty: string;
  value?: string;
  workspaceId: string; // Workspace ID to associate contracts with workspaces
}

interface ContractListProps {
  status: "all" | "draft" | "review" | "active" | "expired";
  searchQuery?: string;
  onContractSelect: (contractId: string) => void;
  onPreviewContract?: (contractId: string) => void;
  selectedContracts?: string[];
  onSelectContract?: (contractId: string, isChecked: boolean) => void;
  onSelectAll?: (isChecked: boolean) => void;
}

const ContractList = ({
  status = "all",
  searchQuery = "",
  onContractSelect,
  onPreviewContract,
  selectedContracts = [],
  onSelectContract,
  onSelectAll,
}: ContractListProps) => {
  // Get workspace context
  const { currentWorkspace, canAccessContent } = useWorkspace();
  const { fetch } = useApi();

  // State for contracts data
  const [contracts, setContracts] = useState<Contract[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);



  // State for delete confirmation dialog
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [contractToDelete, setContractToDelete] = useState<string | null>(null);
  const [deleteLoading, setDeleteLoading] = useState(false);

  // State for document generator dialog
  const [documentGeneratorOpen, setDocumentGeneratorOpen] = useState(false);
  const [contractForGeneration, setContractForGeneration] = useState<string | null>(null);

  // Fetch contracts from API
  useEffect(() => {
    const fetchContracts = async () => {
      setLoading(true);
      setError(null);

      try {
        // Only fetch contracts for the current workspace if one is selected
        if (!currentWorkspace?.id) {
          setLoading(false);
          return;
        }

        const params = {
          workspace_id: currentWorkspace.id,
        };

        const result = await fetch(
          async (token) => ContractService.getContracts(params, token),
          "Loading contracts...",
          "Failed to load contracts"
        );

        if (result) {
          // Map API contracts to UI contracts
          const mappedContracts: Contract[] = result.map((contract: ApiContract) => ({
            id: contract.id,
            title: contract.title,
            type: contract.type,
            status: contract.status,
            createdBy: {
              name: contract.created_by?.name || 'Unknown',
              id: contract.created_by?.id || 'unknown-id',
              // Generate initials from name if not provided
              initials: contract.created_by?.name
                ? contract.created_by.name
                    .split(' ')
                    .map(n => n[0])
                    .join('')
                    .toUpperCase()
                    .substring(0, 2)
                : 'UN',
              // Generate avatar from name
              avatar: contract.created_by?.name
                ? `https://api.dicebear.com/7.x/avataaars/svg?seed=${contract.created_by.name.toLowerCase().replace(/\s/g, '')}`
                : undefined
            },
            createdDate: contract.created_at,
            expiryDate: contract.expiry_date,
            counterparty: contract.counterparty || 'N/A',
            value: contract.value,
            workspaceId: contract.workspace_id
          }));

          setContracts(mappedContracts);
        }
      } catch (err) {
        console.error("Error fetching contracts:", err);
        setError("Failed to load contracts. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    fetchContracts();
  }, [currentWorkspace, fetch]);

  // Filter contracts based on workspace, status, and search query
  const filteredContracts = contracts.filter((contract) => {
    // Filter by workspace - only show contracts from the current workspace
    // AND ensure user has access to the workspace the contract belongs to
    const matchesWorkspace = currentWorkspace ?
      contract.workspaceId === currentWorkspace.id && canAccessContent(contract.workspaceId) :
      false;

    // Filter by status
    const matchesStatus = status === "all" || contract.status === status;

    // Filter by search query
    const matchesSearch =
      contract.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      contract.counterparty.toLowerCase().includes(searchQuery.toLowerCase()) ||
      contract.type.toLowerCase().includes(searchQuery.toLowerCase());

    return matchesWorkspace && matchesStatus && matchesSearch;
  });

  // Enhanced table state management
  const tableState = useTableState({
    data: filteredContracts,
    getRowId: (contract: Contract) => contract.id,
    defaultSort: { key: "createdDate", direction: "desc" },
  });

  // Update selection handlers to use table state
  const handleRowSelect = (contractId: string, selected: boolean) => {
    tableState.handleRowSelect(contractId, selected);
    if (onSelectContract) {
      onSelectContract(contractId, selected);
    }
  };

  const handleSelectAll = (selected: boolean) => {
    tableState.handleSelectAll(selected);
    if (onSelectAll) {
      onSelectAll(selected);
    }
  };

  const getStatusBadge = (status: Contract["status"]) => {
    switch (status) {
      case "draft":
        return <Badge variant="outline">Draft</Badge>;
      case "review":
        return <Badge variant="secondary">In Review</Badge>;
      case "active":
        return <Badge variant="default">Active</Badge>;
      case "expired":
        return <Badge variant="destructive">Expired</Badge>;
      case "rejected":
        return <Badge variant="destructive">Rejected</Badge>;
      default:
        return null;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Column definitions for enhanced table
  const columns: ColumnDef<Contract>[] = [
    {
      key: "title",
      label: "Contract",
      sortable: true,
      render: (_: any, contract: Contract) => (
        <div className="flex items-center gap-3">
          <div className="h-8 w-8 rounded-full bg-muted flex items-center justify-center text-xs">
            {contract.createdBy.initials}
          </div>
          <div>
            <div
              className="font-medium cursor-pointer hover:underline transition-colors"
              onClick={() => onContractSelect(contract.id)}
            >
              {contract.title}
            </div>
            <div className="text-xs text-muted-foreground">
              {contract.value && `Value: ${contract.value}`}
            </div>
          </div>
        </div>
      ),
    },
    {
      key: "type",
      label: "Type",
      sortable: true,
    },
    {
      key: "counterparty",
      label: "Counterparty",
      sortable: true,
    },
    {
      key: "createdDate",
      label: "Created",
      sortable: true,
      render: (value: any) => formatDate(value),
    },
    {
      key: "expiryDate",
      label: "Expiry",
      sortable: true,
      render: (value: any) => value ? formatDate(value) : "-",
    },
    {
      key: "status",
      label: "Status",
      sortable: true,
      render: (value: any) => getStatusBadge(value),
    },
    {
      key: "actions",
      label: "Actions",
      sortable: false,
      width: "120px",
      render: (_: any, contract: Contract) => (
        <div className="flex items-center gap-2 table-row-actions">
          {onPreviewContract && (
            <Button
              variant="ghost"
              size="icon"
              onClick={(e) => {
                e.stopPropagation();
                onPreviewContract(contract.id);
              }}
              title="Quick Preview"
              className="h-8 w-8 hover:bg-muted"
            >
              <Eye className="h-4 w-4" />
            </Button>
          )}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-8 w-8 hover:bg-muted">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onContractSelect(contract.id)}>
                <ExternalLink className="mr-2 h-4 w-4" /> Open
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleEditContract(contract.id)}>
                <FileEdit className="mr-2 h-4 w-4" /> Edit
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleDuplicateContract(contract.id)}>
                <Copy className="mr-2 h-4 w-4" /> Duplicate
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleDownloadContract(contract.id)}>
                <Download className="mr-2 h-4 w-4" /> Download
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleEmailContract(contract.id)}>
                <Mail className="mr-2 h-4 w-4" /> Email
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleGenerateDocument(contract.id)}>
                <FileText className="mr-2 h-4 w-4" /> Generate Document
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                className="text-destructive"
                onClick={() => handleDeleteClick(contract.id)}
              >
                <Trash2 className="mr-2 h-4 w-4" /> Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      ),
    },
  ];

  // Handler functions for contract actions
  const handleEditContract = (contractId: string) => {
    // Find the contract to edit
    const contract = contracts.find(c => c.id === contractId);
    if (!contract) return;

    // In a real app, this would navigate to the edit page or open an edit modal
    toast({
      title: "Editing Contract",
      description: `Now editing: ${contract.title}`,
    });

    // For demo purposes, we'll just call onContractSelect to open the contract
    onContractSelect(contractId);
  };

  const handleDuplicateContract = (contractId: string) => {
    // Find the contract to duplicate
    const contract = contracts.find(c => c.id === contractId);
    if (!contract) return;

    toast({
      title: "Contract Duplicated",
      description: `Created a copy of: ${contract.title}`,
    });
  };

  const handleDownloadContract = (contractId: string) => {
    // Find the contract to download
    const contract = contracts.find(c => c.id === contractId);
    if (!contract) return;

    // In a real app, this would trigger a download of the contract
    toast({
      title: "Downloading Contract",
      description: `Preparing ${contract.title} for download...`,
    });

    // Simulate download completion after a delay
    setTimeout(() => {
      toast({
        title: "Download Complete",
        description: `${contract.title} has been downloaded.`,
      });
    }, 2000);
  };

  const handleEmailContract = (contractId: string) => {
    // Find the contract to email
    const contract = contracts.find(c => c.id === contractId);
    if (!contract) return;

    toast({
      title: "Email Contract",
      description: `Preparing to email: ${contract.title}`,
    });
  };

  const handleGenerateDocument = (contractId: string) => {
    // Find the contract to generate document for
    const contract = contracts.find(c => c.id === contractId);
    if (!contract) return;

    setContractForGeneration(contractId);
    setDocumentGeneratorOpen(true);
  };

  const handleDeleteClick = (contractId: string) => {
    // Find the contract to delete
    const contract = contracts.find(c => c.id === contractId);
    if (!contract) return;

    // Set the contract to delete and open the confirmation dialog
    setContractToDelete(contractId);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!contractToDelete) return;

    // Find the contract to delete
    const contract = contracts.find(c => c.id === contractToDelete);
    if (!contract) return;

    setDeleteLoading(true);

    try {
      // Call the API to delete the contract
      await fetch(
        async (token) => ContractService.deleteContract(contractToDelete, token),
        "Deleting contract...",
        "Failed to delete contract"
      );

      // Update the local state by removing the deleted contract
      setContracts(contracts.filter(c => c.id !== contractToDelete));

      toast({
        title: "Contract Deleted",
        description: `${contract.title} has been deleted.`,
        variant: "destructive",
      });
    } catch (err) {
      console.error("Error deleting contract:", err);
    } finally {
      // Close the dialog and reset the contract to delete
      setDeleteLoading(false);
      setDeleteDialogOpen(false);
      setContractToDelete(null);
    }
  };

  return (
    <div className="w-full">
      {loading ? (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <Loader2 className="h-12 w-12 text-primary animate-spin mb-4" />
          <h3 className="text-lg font-medium">Loading contracts...</h3>
          <p className="text-muted-foreground mt-2">
            Please wait while we fetch your contracts
          </p>
        </div>
      ) : error ? (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <FileText className="h-12 w-12 text-destructive mb-4" />
          <h3 className="text-lg font-medium">Error loading contracts</h3>
          <p className="text-muted-foreground mt-2">{error}</p>
        </div>
      ) : filteredContracts.length === 0 ? (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <FileText className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium">No contracts found</h3>
          <p className="text-muted-foreground mt-2">
            {searchQuery
              ? "Try adjusting your search query"
              : "Create your first contract to get started"}
          </p>
        </div>
      ) : (
        <div className="rounded-md border">
          <EnhancedTable
            data={tableState.sortedData}
            columns={columns}
            sortConfig={tableState.sortConfig}
            onSort={tableState.handleSort}
            selectedRows={onSelectContract ? tableState.selectedRows : []}
            onRowSelect={onSelectContract ? handleRowSelect : undefined}
            onSelectAll={onSelectAll ? handleSelectAll : undefined}
            onRowClick={(contract: Contract) => onContractSelect(contract.id)}
            hoverable={true}
            stickyHeader={false}
            keyboardNavigation={true}
            loading={loading}
            emptyMessage="No contracts found"
            className="rounded-md"
          />

        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure you want to delete this contract?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the contract
              and remove it from our servers.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setContractToDelete(null)}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              disabled={deleteLoading}
            >
              {deleteLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                "Delete"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Document Generator Dialog */}
      <Dialog open={documentGeneratorOpen} onOpenChange={setDocumentGeneratorOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Generate Professional Document</DialogTitle>
          </DialogHeader>
          {contractForGeneration && (
            <AdvancedDocumentGenerator
              contractId={contractForGeneration}
              contractTitle={contracts.find(c => c.id === contractForGeneration)?.title}
              onDocumentGenerated={(fileInfo) => {
                toast({
                  title: "Document Generated",
                  description: `${fileInfo.filename} has been generated successfully`,
                });
              }}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ContractList;
