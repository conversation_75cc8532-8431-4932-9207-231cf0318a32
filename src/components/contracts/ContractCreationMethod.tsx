import React, { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import ImportDocumentModal from "./ImportDocumentModal";
import { ArrowLeft, ArrowRight, FileText, Wand2, Sparkles, Clock, Import, Copy, Users, Check, PlusCircle, BookTemplate, Lightbulb } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Progress } from "@/components/ui/progress";

const ContractCreationMethod = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [activeTab, setActiveTab] = useState("template");
  const [aiPrompt, setAiPrompt] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [showTemplateFeatures, setShowTemplateFeatures] = useState(false);
  const [showImportModal, setShowImportModal] = useState(false);

  // Check if we're on the import route and show the import modal
  useEffect(() => {
    if (location.pathname === "/contracts/import") {
      setShowImportModal(true);
    }
  }, [location.pathname]);

  // Handle import modal close
  const handleImportModalChange = (open: boolean) => {
    setShowImportModal(open);
    // If we're on the import route and the modal is closed, navigate back to create
    if (!open && location.pathname === "/contracts/import") {
      navigate("/contracts/create");
    }
  };

  const handleUseTemplate = () => {
    navigate("/contracts/templates");
  };

  const handleStartFromScratch = () => {
    navigate("/contracts/wizard");
  };

  const handleAIGenerate = () => {
    if (!aiPrompt.trim()) return;

    setIsGenerating(true);
    // Simulate AI processing
    setTimeout(() => {
      setIsGenerating(false);
      navigate("/contracts/wizard?useAI=true&prompt=" + encodeURIComponent(aiPrompt));
    }, 2000);
  };

  return (
    <div className="w-full h-full bg-background p-4 overflow-auto">
      <div className="flex items-center justify-between mb-4">
        <div>
          <h3 className="text-2xl font-medium">Create New Contract</h3>
          <p className="text-sm text-muted-foreground">
            Select how you'd like to create your new contract
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="default"
            size="sm"
            onClick={handleStartFromScratch}
            className="flex items-center h-9"
          >
            <Wand2 className="mr-2 h-4 w-4" />
            Start from Scratch
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate("/contracts")}
            className="flex items-center h-9"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Contracts
          </Button>
        </div>
      </div>

      {/* Prominent floating "Start from Scratch" button for small screens */}
      <div className="fixed bottom-4 right-4 md:hidden z-10">
        <Button
          size="sm"
          className="rounded-full h-12 w-12 p-0 flex items-center justify-center shadow-lg bg-primary"
          onClick={handleStartFromScratch}
        >
          <Wand2 className="h-5 w-5" />
        </Button>
      </div>

      <div className="flex flex-col lg:flex-row gap-4">
        {/* Left Column: Main Content with Tabs */}
        <div className="flex-1">
          <div className="mb-6">
            <Tabs
              defaultValue="template"
              className="w-full"
              value={activeTab}
              onValueChange={setActiveTab}
            >
              <TabsList className="grid w-full grid-cols-3 mb-4">
                <TabsTrigger value="template" className="flex items-center gap-2 text-sm">
                  <FileText className="h-4 w-4" />
                  <span>Use Template</span>
                </TabsTrigger>
                <TabsTrigger value="scratch" className="flex items-center gap-2 text-sm">
                  <Wand2 className="h-4 w-4" />
                  <span>Start from Scratch</span>
                </TabsTrigger>
                <TabsTrigger value="ai" className="flex items-center gap-2 text-sm">
                  <Sparkles className="h-4 w-4" />
                  <span>AI-Assisted</span>
                </TabsTrigger>
              </TabsList>

              <TabsContent value="template" className="space-y-6">
                <Card>
                  <CardHeader className="pb-2">
                    <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center mb-3">
                      <FileText className="h-5 w-5 text-primary" />
                    </div>
                    <CardTitle className="text-sm font-medium">Use Template</CardTitle>
                    <CardDescription className="text-sm">
                      Quickly create a contract using a professionally drafted template.
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="mb-6">
                      <p className="text-sm text-muted-foreground mb-2">
                        <strong>How it works:</strong> Select a template that matches your contract type, then fill in the details and make any edits you need. Templates are designed to cover common use cases and legal requirements.
                      </p>

                      <div className="flex items-center mb-3 mt-3 cursor-pointer" onClick={() => setShowTemplateFeatures(!showTemplateFeatures)}>
                        <Button variant="ghost" size="sm" className="h-6 px-1.5 text-xs">
                          {showTemplateFeatures ?
                            <Check className="h-3 w-3 mr-1.5 text-primary" /> :
                            <PlusCircle className="h-3 w-3 mr-1.5 text-primary" />
                          }
                          {showTemplateFeatures ? "Key Features" : "Show Key Features"}
                        </Button>
                      </div>

                      {showTemplateFeatures && (
                        <div className="space-y-2 mb-4 bg-secondary/20 p-3 rounded-md">
                          <div className="flex items-start gap-2">
                            <div className="w-6 h-6 rounded-full bg-green-100 flex items-center justify-center flex-shrink-0">
                              <Check className="h-3 w-3 text-green-600" />
                            </div>
                            <div>
                              <h4 className="text-xs font-medium">Legally Vetted</h4>
                              <p className="text-xs text-muted-foreground">All templates are reviewed by legal professionals and updated regularly</p>
                            </div>
                          </div>

                          <div className="flex items-start gap-2">
                            <div className="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0">
                              <BookTemplate className="h-3 w-3 text-blue-600" />
                            </div>
                            <div>
                              <h4 className="text-xs font-medium">Customizable</h4>
                              <p className="text-xs text-muted-foreground">Edit any part of the template to fit your specific needs</p>
                            </div>
                          </div>

                          <div className="flex items-start gap-2">
                            <div className="w-6 h-6 rounded-full bg-purple-100 flex items-center justify-center flex-shrink-0">
                              <Lightbulb className="h-3 w-3 text-purple-600" />
                            </div>
                            <div>
                              <h4 className="text-xs font-medium">Smart Suggestions</h4>
                              <p className="text-xs text-muted-foreground">Get contextual suggestions for clauses based on your industry and needs</p>
                            </div>
                          </div>
                        </div>
                      )}

                      <div className="grid grid-cols-2 gap-2 mb-4">
                        <Card className="border border-muted p-2 hover:border-primary/50 hover:bg-primary/5 transition-colors cursor-pointer">
                          <div className="font-medium text-xs mb-0.5">NDA</div>
                          <div className="text-xs text-muted-foreground">
                            Protect confidential information
                          </div>
                        </Card>
                        <Card className="border border-muted p-2 hover:border-primary/50 hover:bg-primary/5 transition-colors cursor-pointer">
                          <div className="font-medium text-xs mb-0.5">Service Agreement</div>
                          <div className="text-xs text-muted-foreground">
                            Define service terms
                          </div>
                        </Card>
                        <Card className="border border-muted p-2 hover:border-primary/50 hover:bg-primary/5 transition-colors cursor-pointer">
                          <div className="font-medium text-xs mb-0.5">Employment</div>
                          <div className="text-xs text-muted-foreground">
                            Hire employees properly
                          </div>
                        </Card>
                        <Card className="border border-muted p-2 hover:border-primary/50 hover:bg-primary/5 transition-colors cursor-pointer">
                          <div className="font-medium text-xs mb-0.5">Software License</div>
                          <div className="text-xs text-muted-foreground">
                            License your software
                          </div>
                        </Card>
                      </div>
                    </div>
                    <Button className="w-full h-9 text-sm" onClick={handleUseTemplate}>Browse All Templates</Button>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="scratch" className="space-y-6">
                <Card>
                  <CardHeader className="pb-2">
                    <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center mb-3">
                      <Wand2 className="h-5 w-5 text-primary" />
                    </div>
                    <CardTitle className="text-sm font-medium">Start from Scratch</CardTitle>
                    <CardDescription className="text-sm">
                      Create a fully customized contract from the ground up.
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="mb-6">
                      <p className="text-sm text-muted-foreground mb-4">
                        <strong>How it works:</strong> Use our step-by-step wizard to build your contract, or jump straight into the editor for full control.
                      </p>

                      <div className="flex mb-4 bg-secondary/20 p-3 rounded-md">
                        <div className="flex-1">
                          <h4 className="text-sm font-medium mb-1.5">Guided Contract Creation Steps</h4>
                          <ol className="list-decimal ml-4 text-xs text-muted-foreground space-y-0.5">
                            <li>Choose jurisdiction and contract type</li>
                            <li>Add parties' information</li>
                            <li>Specify contract terms and details</li>
                            <li>Select appropriate legal clauses</li>
                            <li>Add industry-specific provisions</li>
                            <li>Upload attachments if needed</li>
                            <li>Set approval workflow</li>
                            <li>Review and export your contract</li>
                          </ol>
                        </div>
                        <div className="w-24 hidden md:block">
                          <div className="h-full border-l border-dashed border-muted-foreground/30 ml-4 pl-4 flex flex-col justify-center">
                            <div className="text-sm font-medium text-muted-foreground mb-1">Benefits</div>
                            <div className="text-xs text-muted-foreground">
                              <div className="flex items-center mb-0.5">
                                <Check className="h-2.5 w-2.5 mr-1 text-green-600" />
                                <span>Full control</span>
                              </div>
                              <div className="flex items-center mb-0.5">
                                <Check className="h-2.5 w-2.5 mr-1 text-green-600" />
                                <span>Customized</span>
                              </div>
                              <div className="flex items-center">
                                <Check className="h-2.5 w-2.5 mr-1 text-green-600" />
                                <span>Guided process</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="grid md:grid-cols-2 gap-3 mb-4">
                        <div className="bg-muted rounded-lg p-2.5">
                          <div className="text-sm font-medium mb-0.5">Ideal for unique agreements</div>
                          <div className="text-sm text-muted-foreground">
                            Perfect when you need a contract tailored to your specific circumstances
                          </div>
                        </div>
                        <div className="bg-muted rounded-lg p-2.5">
                          <div className="text-sm font-medium mb-0.5">Save and resume anytime</div>
                          <div className="text-sm text-muted-foreground">
                            Your progress is automatically saved so you can come back later
                          </div>
                        </div>
                      </div>
                    </div>
                    <Button className="w-full h-9 text-sm" onClick={handleStartFromScratch}>Start from Scratch</Button>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="ai" className="space-y-6">
                <Card>
                  <CardHeader className="pb-2">
                    <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center mb-3">
                      <Sparkles className="h-5 w-5 text-primary" />
                    </div>
                    <div className="flex items-center gap-2">
                      <CardTitle className="text-sm font-medium">AI-Assisted</CardTitle>
                      <Badge className="bg-yellow-100 hover:bg-yellow-200 text-yellow-800 border-yellow-300 text-sm py-0 px-1.5">Coming Soon</Badge>
                    </div>
                    <CardDescription className="text-sm">
                      Let our AI draft a contract for you in seconds based on your needs.
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="mb-4">
                      <p className="text-sm text-muted-foreground mb-4">
                        <strong>How it works:</strong> Describe your contract in plain English, and our AI will generate a draft tailored to your needs. You'll be able to review, edit, and finalize the contract before saving or sending.
                      </p>

                      <div className="mb-6 space-y-4">
                        <div className="bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-950/30 dark:to-blue-950/30 rounded-lg p-3">
                          <div className="text-sm font-medium mb-1.5">Describe what you need</div>
                          <Textarea
                            placeholder="E.g.: I need a consulting services agreement between my company and a freelance graphic designer for a 3-month project with monthly payments..."
                            className="bg-white/80 dark:bg-black/20 resize-none text-xs"
                            rows={3}
                            value={aiPrompt}
                            onChange={(e) => setAiPrompt(e.target.value)}
                            disabled
                          />
                          <div className="text-sm text-muted-foreground mt-1.5">
                            Include important details like parties involved, contract purpose, key terms, duration, and any special requirements.
                          </div>
                        </div>

                        <div className="flex flex-col md:flex-row gap-3">
                          <div className="flex-1 bg-muted rounded-lg p-2.5">
                            <div className="flex items-start gap-1.5">
                              <Lightbulb className="h-3 w-3 text-amber-500 mt-0.5" />
                              <div>
                                <div className="text-xs font-medium mb-0.5">AI suggests clauses</div>
                                <div className="text-xs text-muted-foreground">
                                  Based on your description, the AI will suggest appropriate clauses
                                </div>
                              </div>
                            </div>
                          </div>
                          <div className="flex-1 bg-muted rounded-lg p-2.5">
                            <div className="flex items-start gap-1.5">
                              <Check className="h-3 w-3 text-green-500 mt-0.5" />
                              <div>
                                <div className="text-xs font-medium mb-0.5">Review and edit</div>
                                <div className="text-xs text-muted-foreground">
                                  You'll always have full control to edit the generated contract
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <Button
                      className="w-full h-9 text-sm bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600 opacity-70"
                      disabled={true}
                    >
                      <Sparkles className="mr-2 h-4 w-4" />
                      Coming Soon
                    </Button>
                    <div className="text-sm text-center text-muted-foreground mt-2">We're working hard to bring this feature to you soon!</div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>

        {/* Right Column: Sidebar */}
        <div className="w-full lg:w-[300px] shrink-0">
          {/* Quick Templates */}
          <Card className="mb-6">
            <CardHeader className="pb-3">
              <div className="flex items-center">
                <div className="w-5 h-5 bg-amber-400/20 text-amber-500 flex items-center justify-center rounded-full mr-2">
                  <span className="text-xs">★</span>
                </div>
                <CardTitle className="text-sm font-medium">Quick Templates</CardTitle>
              </div>
              <CardDescription className="text-sm">
                Your most used templates
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <QuickTemplateItem
                title="NDA"
                subtitle="Non-Disclosure Agreement"
                icon="🔒"
                onClick={() => navigate("/contracts/wizard?template=nda")}
              />
              <QuickTemplateItem
                title="Services"
                subtitle="Service Agreement"
                icon="🛠️"
                onClick={() => navigate("/contracts/wizard?template=service")}
              />
              <QuickTemplateItem
                title="Employment"
                subtitle="Employment Contract"
                icon="👤"
                onClick={() => navigate("/contracts/wizard?template=employment")}
              />
              <div className="text-xs text-right text-muted-foreground mt-2">
                Based on your usage history
              </div>
            </CardContent>
          </Card>

          {/* Resume Draft */}
          <Card className="mb-6">
            <CardHeader className="pb-3">
              <div className="flex items-center">
                <Clock className="w-3 h-3 mr-2 text-muted-foreground" />
                <CardTitle className="text-sm font-medium">Resume Draft</CardTitle>
              </div>
              <CardDescription className="text-sm">
                Continue where you left off
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <DraftItem
                title="Service Agreement - Acme Corp"
                date="Jul 15, 10:30 AM"
                progress={60}
                onClick={() => navigate("/contracts/wizard?draft=1")}
              />

              <DraftItem
                title="Employment Contract - John Smith"
                date="Jul 14, 4:45 PM"
                progress={30}
                onClick={() => navigate("/contracts/wizard?draft=2")}
              />
            </CardContent>
          </Card>

          {/* Other Options */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Other Options</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button variant="outline" className="w-full justify-start h-auto py-2" onClick={() => handleImportModalChange(true)}>
                <Import className="mr-2 h-4 w-4" />
                <div className="flex flex-col items-start">
                  <span className="text-sm font-medium">Import Document</span>
                  <span className="text-sm text-muted-foreground">Upload existing documents</span>
                </div>
              </Button>
              <Button variant="outline" className="w-full justify-start h-auto py-2" onClick={() => navigate("/clone")}>
                <Copy className="mr-2 h-4 w-4" />
                <div className="flex flex-col items-start">
                  <span className="text-sm font-medium">Clone Existing Contract</span>
                  <span className="text-sm text-muted-foreground">Start from your own contract</span>
                </div>
              </Button>
              <Button variant="outline" className="w-full justify-start h-auto py-2" onClick={() => navigate("/request")}>
                <Users className="mr-2 h-4 w-4" />
                <div className="flex flex-col items-start">
                  <span className="text-sm font-medium">Request from Legal Team</span>
                  <span className="text-sm text-muted-foreground">Get expert assistance</span>
                </div>
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Import Document Modal */}
      <ImportDocumentModal
        open={showImportModal}
        onOpenChange={handleImportModalChange}
      />
    </div>
  );
};

// Enhanced QuickTemplateItem component with icon support
const QuickTemplateItem = ({
  title,
  subtitle,
  icon,
  onClick
}: {
  title: string;
  subtitle: string;
  icon?: string;
  onClick: () => void;
}) => {
  return (
    <Button
      variant="ghost"
      className="w-full justify-start h-auto py-2 pl-2 pr-2 hover:bg-secondary"
      onClick={onClick}
    >
      <div className="flex items-center w-full">
        <div className="w-6 h-6 rounded-md flex items-center justify-center mr-2 bg-primary/10">
          {icon ? (
            <span className="text-sm">{icon}</span>
          ) : (
            <FileText className="h-3 w-3 text-primary" />
          )}
        </div>
        <div className="flex-1 text-left">
          <div className="font-medium text-sm">{title}</div>
          <div className="text-sm text-muted-foreground">{subtitle}</div>
        </div>
        <ArrowRight className="h-3 w-3 ml-1 text-muted-foreground" />
      </div>
    </Button>
  );
};

// New DraftItem component with progress indicator
const DraftItem = ({
  title,
  date,
  progress,
  onClick
}: {
  title: string;
  date: string;
  progress: number;
  onClick: () => void;
}) => {
  return (
    <div className="space-y-2 bg-secondary/20 p-3 rounded-md hover:bg-secondary/30 transition-colors cursor-pointer" onClick={onClick}>
      <div className="flex flex-col">
        <div className="font-medium text-sm">{title}</div>
        <div className="text-sm text-muted-foreground">Last edited: {date}</div>
      </div>
      <Progress value={progress} className="h-1" />
      <div className="flex justify-between items-center">
        <div className="text-sm text-muted-foreground">{progress}% complete</div>
        <Button variant="ghost" size="sm" className="h-8 px-2 text-sm">
          Continue
          <ArrowRight className="ml-1.5 h-3.5 w-3.5" />
        </Button>
      </div>
    </div>
  );
};

export default ContractCreationMethod;