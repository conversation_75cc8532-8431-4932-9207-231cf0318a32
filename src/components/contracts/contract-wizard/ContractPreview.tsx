import React, { useRef } from 'react';
import html2pdf from 'html2pdf.js';
import { Button } from '@/components/ui/button';
import { useContractWizard } from './ContractWizardContext';
import DocumentExportButton from '../DocumentExportButton';

/**
 * Professional contract preview UI, styled like a formal German PDF contract.
 * - Title centered, bold, large
 * - Parties (Client left, Contractor right)
 * - Dashed dividers
 * - Section headers bold, uppercase, centered
 * - Paragraphs justified, readable font
 * - Scrollable, PDF export
 */
const ContractPreview: React.FC = () => {
  const { data } = useContractWizard();
  const previewRef = useRef<HTMLDivElement>(null);

  const handleExportPDF = () => {
    if (previewRef.current) {
      html2pdf().from(previewRef.current).set({
        margin: 0.5,
        filename: `${data.title || 'contract'}.pdf`,
        html2canvas: { scale: 2 },
        jsPDF: { unit: 'in', format: 'a4', orientation: 'portrait' },
      }).save();
    }
  };

  // Helper: Render address block
  const renderPartyBlock = (party: any, align: 'left' | 'right') => (
    <div className={`w-1/2 ${align === 'left' ? 'text-left' : 'text-right'} font-serif text-[15px] leading-tight`}> 
      <div className="font-bold uppercase tracking-wide text-[15px]">{party?.name || 'Client/Contractor'}</div>
      <div>{party?.address || 'Address'}</div>
      <div>{party?.representative && `Attn: ${party.representative}`}</div>
      <div>{party?.title}</div>
    </div>
  );

  // Helper: Render sections
  const renderSection = (title: string, content: string | React.ReactNode) => (
    <section className="mb-8">
      <h2 className="text-center font-bold uppercase text-[15px] tracking-wider mb-2 mt-6">{title}</h2>
      <div className="border-t border-dashed border-slate-300 mb-4 w-1/2 mx-auto" />
      <div className="text-justify font-serif text-[15px] leading-relaxed whitespace-pre-line">{content}</div>
    </section>
  );

  // Example: Split parties
  const client = data.parties?.[0] || {};
  const contractor = data.parties?.[1] || {};

  // Example: Sections (replace with real data/loop as needed)
  const sections = [
    { title: 'Recitals', content: data.description || '...' },
    { title: 'Term and Termination', content: `This Agreement shall commence on the Effective Date and continue for ${data.duration || '...'}.` },
    { title: 'Payment & Deliverables', content: `Contract Value: ${data.contractValue || '...'} ${data.currency || ''}\nPayment Terms: ${data.paymentTerms || '...'}\nDeliverables: ${(data.deliverables && data.deliverables.length) ? data.deliverables.join(', ') : 'N/A'}` },
    { title: 'Legal Clauses', content: (data.standardClauses && data.standardClauses.length) ? data.standardClauses.join(', ') : 'No clauses selected.' },
    // Add more sections as needed
  ];

  return (
    <div className="relative bg-white rounded-lg border shadow-lg max-w-3xl mx-auto my-8 p-8 font-serif text-neutral-900 min-h-[80vh] max-h-[90vh] overflow-y-auto" style={{ fontFamily: 'Lora, serif' }}>
      {/* PDF Download Button */}
      <Button onClick={handleExportPDF} className="absolute right-8 top-8 z-20" variant="outline" size="sm">
        Download PDF
      </Button>
      <div ref={previewRef} className="pb-8">
        {/* Title */}
        <h1 className="text-center font-bold text-3xl mb-2 tracking-tight leading-snug uppercase font-serif">{data.title || 'Contract Title'}</h1>
        <div className="border-t-2 border-dashed border-slate-300 mb-6 w-1/2 mx-auto" />
        {/* Parties Block */}
        <div className="flex flex-row justify-between mb-8 mt-4 gap-6">
          {renderPartyBlock(client, 'left')}
          {renderPartyBlock(contractor, 'right')}
        </div>
        <div className="border-t border-dashed border-slate-300 mb-6" />
        {/* Sections */}
        {sections.map((section, i) => renderSection(section.title, section.content))}
        {/* Footer (optional page numbers, simulated) */}
        <footer className="mt-10 text-center text-xs text-slate-400">
          — End of Document —
        </footer>
      </div>
    </div>
  );
};

export default ContractPreview;
