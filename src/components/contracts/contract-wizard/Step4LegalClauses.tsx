import React, { useState } from 'react';
import { useContractWizard } from './ContractWizardContext';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Plus, Trash2, ChevronLeft, ChevronRight, Save } from 'lucide-react';
import ClauseLibrary from '@/components/contracts/ClauseLibrary';
import ClauseOrderList from './ClauseOrderList';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';



const Step4LegalClauses: React.FC = () => {
  const { data, setData, nextStep, prevStep } = useContractWizard();
  const [customClauseModal, setCustomClauseModal] = useState(false);
  const [newCustomClause, setNewCustomClause] = useState({ title: '', content: '', category: 'General Provisions', tags: '' });
  const categories = [
    'General Provisions',
    'Definitions',
    'Obligations',
    'Confidentiality',
    'Limitation of Liability',
    'Termination',
    'Dispute Resolution',
    'Intellectual Property',
    // Add more as needed
  ];
  const [touched, setTouched] = useState(false);
  const [libraryClauses, setLibraryClauses] = useState<{ title: string; content: string; category: string; tags: string[] }[]>(
    (data.libraryClauses || []).map((clause: any) => ({
      ...clause,
      category: clause.category || 'General Provisions',
      tags: clause.tags || [],
    }))
  );

  // Validation: at least one clause (standard, library, or custom with title+content)
  const isValid =
    (libraryClauses && libraryClauses.length > 0) ||
    (data.customClauses && data.customClauses.length > 0 && data.customClauses.every((c: { title: string; content: string }) => c.title.trim() && c.content.trim()));

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setTouched(true);
    if (isValid) nextStep();
  };

  const toggleStandardClause = (id: string) => {
    setData(d => ({
      ...d,
      standardClauses: d.standardClauses.includes(id)
        ? d.standardClauses.filter(c => c !== id)
        : [...d.standardClauses, id],
    }));
  };

  const handleAddCustomClause = () => {
    if (!newCustomClause.title.trim() || !newCustomClause.content.trim()) return;
    setData(d => ({
      ...d,
      customClauses: [
        ...(d.customClauses || []),
        { title: newCustomClause.title.trim(), content: newCustomClause.content.trim() },
      ],
    }));
    setNewCustomClause({ title: '', content: '', category: 'General Provisions', tags: '' });
    setCustomClauseModal(false);
  };

  const removeCustomClause = (idx: number) => {
    setData(d => ({ ...d, customClauses: d.customClauses.filter((_: any, i: number) => i !== idx) }));
  };

  const handleSelectLibraryClause = (clause: { title: string; content: string; category?: string; tags?: string[] }) => {
    setLibraryClauses(prev => {
      if (prev.some(c => c.title === clause.title && c.content === clause.content)) return prev;
      const updated = [
        ...prev,
        {
          ...clause,
          category: clause.category || 'General Provisions',
          tags: clause.tags || [],
        },
      ];
      setData(d => ({ ...d, libraryClauses: updated }));
      return updated;
    });
  };

  const removeLibraryClause = (idx: number) => {
    setLibraryClauses(prev => {
      const updated = prev.filter((_, i) => i !== idx);
      setData(d => ({ ...d, libraryClauses: updated }));
      return updated;
    });
  };

  return (
    <form className="space-y-6" onSubmit={handleSubmit}>
      {/* Tabbed layout for clause management */}
      <Tabs defaultValue="library" className="w-full">
        <TabsList className="mb-6 bg-slate-100 dark:bg-slate-800/50 p-1 rounded-md">
          <TabsTrigger value="library" className="data-[state=active]:bg-white dark:data-[state=active]:bg-slate-950 data-[state=active]:shadow-sm">Clause Library</TabsTrigger>
          <TabsTrigger value="custom" className="data-[state=active]:bg-white dark:data-[state=active]:bg-slate-950 data-[state=active]:shadow-sm">Custom Clauses</TabsTrigger>
        </TabsList>
        <TabsContent value="library">
          <div className="max-w-xl">
            <div className="mb-3">
              <Label className="text-sm font-medium">Add from Clause Library</Label>
              <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">Browse and add standard clauses from the library to this contract.</p>
            </div>

            <ClauseLibrary onSelectClause={clause => handleSelectLibraryClause({ title: clause.title, content: clause.content })} />

            {libraryClauses.length > 0 && (
              <div className="mt-5 space-y-2">
                <h3 className="text-sm font-medium">Selected Clauses</h3>
                <p className="text-xs text-slate-500">Drag to reorder clauses as they will appear in the contract.</p>
                <ClauseOrderList
                  clauses={libraryClauses}
                  onMove={(from, to) => {
                    if (to < 0 || to >= libraryClauses.length) return;
                    const updated = [...libraryClauses];
                    const [moved] = updated.splice(from, 1);
                    updated.splice(to, 0, moved);
                    setLibraryClauses(updated);
                    setData(d => ({ ...d, libraryClauses: updated }));
                  }}
                  onRemove={removeLibraryClause}
                />
              </div>
            )}
          </div>
        </TabsContent>
        {/* Custom Clauses Tab */}
        <TabsContent value="custom">
          <div className="max-w-xl">
            <div className="mb-3">
              <Label className="text-sm font-medium">Custom Clauses</Label>
              <p className="text-xs text-slate-500 mt-1">Add your own custom clauses specific to this contract.</p>
            </div>

            <div className="flex items-center gap-2 mb-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => setCustomClauseModal(true)}
                className="h-8 px-3 bg-white dark:bg-slate-950 border-slate-200 dark:border-slate-700 text-sm"
              >
                <Plus className="h-3 w-3 mr-1" />
                Add Custom Clause
              </Button>
            </div>

            {data.customClauses.length === 0 ? (
              <div className="bg-slate-50 dark:bg-slate-800/50 rounded-lg p-5 text-center border border-slate-100 dark:border-slate-700">
                <p className="text-xs text-slate-500 dark:text-slate-400">No custom clauses added yet.</p>
                <p className="text-xs text-slate-400 dark:text-slate-500 mt-1">Click the button above to add your first custom clause.</p>
              </div>
            ) : (
              <div className="space-y-4">
                {data.customClauses.map((clause: { title: string; content: string; category?: string; tags?: string[] }, idx: number) => {
                  // Check if clause already exists in library
                  const alreadyInLibrary = libraryClauses.some(c => c.title === clause.title && c.content === clause.content);
                  const handleSaveToLibrary = () => {
                    if (alreadyInLibrary || !clause.title.trim() || !clause.content.trim()) return;
                    const updated = [
                      ...libraryClauses,
                      {
                        title: clause.title,
                        content: clause.content,
                        category: clause.category || 'General Provisions',
                        tags: clause.tags || [],
                      },
                    ];
                    setLibraryClauses(updated);
                    setData(d => ({ ...d, libraryClauses: updated }));
                  };
                  return (
                    <div key={idx} className="border border-slate-200 dark:border-slate-700 rounded-lg overflow-hidden bg-white dark:bg-slate-900">
                      <div className="bg-slate-50 dark:bg-slate-800/50 px-3 py-2 border-b border-slate-100 dark:border-slate-700 flex items-center justify-between">
                        <h3 className="text-xs font-medium text-slate-700 dark:text-slate-300">
                          {clause.title || `Clause #${idx + 1}`}
                        </h3>
                        <div className="flex items-center gap-1">
                          <Button
                            type="button"
                            size="sm"
                            variant={alreadyInLibrary ? "secondary" : "ghost"}
                            className={alreadyInLibrary ? "text-slate-400 h-7 text-xs" : "text-blue-600 dark:text-blue-400 h-7 hover:bg-blue-50 dark:hover:bg-blue-950/50 text-xs"}
                            onClick={handleSaveToLibrary}
                            aria-label={alreadyInLibrary ? "Already in library" : "Save to clause library"}
                            disabled={alreadyInLibrary || !clause.title.trim() || !clause.content.trim()}
                            title={alreadyInLibrary ? "Already saved to library" : "Save to clause library"}
                          >
                            <Save className="h-3 w-3 mr-1" />
                            {alreadyInLibrary ? "Saved" : "Save"}
                          </Button>
                          <Button
                            type="button"
                            size="sm"
                            variant="ghost"
                            className="text-destructive h-7 hover:bg-red-50 dark:hover:bg-red-950/50 text-xs"
                            onClick={() => removeCustomClause(idx)}
                            aria-label="Remove custom clause"
                          >
                            <Trash2 className="h-3 w-3 mr-1" />
                            Remove
                          </Button>
                        </div>
                      </div>
                      <div className="p-3 space-y-3">
                        <div className="space-y-1.5">
                          <Label className="text-xs font-medium">Clause Title</Label>
                          <Input
                            className="h-9 bg-white dark:bg-slate-950 border-slate-200 dark:border-slate-700"
                            value={clause.title}
                            onChange={e => setData(d => ({
                              ...d,
                              customClauses: d.customClauses.map((c: any, i: number) => i === idx ? { ...c, title: e.target.value } : c)
                            }))}
                            placeholder={`Enter clause title`}
                          />
                        </div>
                        <div className="space-y-1.5">
                          <Label className="text-xs font-medium">Clause Content</Label>
                          <textarea
                            className="w-full border border-slate-200 dark:border-slate-700 rounded-md px-3 py-2 min-h-[100px] bg-white dark:bg-slate-950"
                            value={clause.content}
                            onChange={e => setData(d => ({
                              ...d,
                              customClauses: d.customClauses.map((c: any, i: number) => i === idx ? { ...c, content: e.target.value } : c)
                            }))}
                            placeholder="Enter the clause text here..."
                            rows={3}
                          />
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>

      {/* Modal for adding custom clause */}
      {customClauseModal && (
        <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-slate-900 rounded-lg shadow-lg p-5 w-[500px] max-w-full modal-content">
            <div className="mb-4">
              <h3 className="text-base font-medium mb-1">Add Custom Clause</h3>
              <p className="text-xs text-slate-500 dark:text-slate-400">Create a new clause specific to this contract</p>
            </div>

            <div className="space-y-4 mb-4">
              <div className="space-y-1.5">
                <Label className="text-xs font-medium">Clause Title <span className="text-destructive">*</span></Label>
                <Input
                  className="h-9 border-slate-200 dark:border-slate-700"
                  value={newCustomClause.title}
                  onChange={e => setNewCustomClause(c => ({ ...c, title: e.target.value }))}
                  placeholder="e.g. Intellectual Property Rights"
                  style={{backgroundColor: 'inherit'}}
                />
              </div>

              <div className="space-y-1.5">
                <Label className="text-xs font-medium">Clause Content <span className="text-destructive">*</span></Label>
                <textarea
                  className="w-full border border-slate-200 dark:border-slate-700 rounded-md px-3 py-2 min-h-[120px]"
                  value={newCustomClause.content}
                  onChange={e => setNewCustomClause(c => ({ ...c, content: e.target.value }))}
                  placeholder="Enter the full text of your clause here..."
                  rows={5}
                  style={{backgroundColor: 'inherit'}}
                />
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div className="space-y-1.5">
                  <Label className="text-xs font-medium">Category</Label>
                  <Select
                    value={newCustomClause.category}
                    onValueChange={value => setNewCustomClause(c => ({ ...c, category: value }))}
                  >
                    <SelectTrigger className="h-9 border-slate-200 dark:border-slate-700" style={{backgroundColor: 'inherit'}}>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map(cat => (
                        <SelectItem key={cat} value={cat}>{cat}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-1.5">
                  <Label className="text-xs font-medium">Tags (comma separated)</Label>
                  <Input
                    className="h-9 border-slate-200 dark:border-slate-700"
                    value={newCustomClause.tags}
                    onChange={e => setNewCustomClause(c => ({ ...c, tags: e.target.value }))}
                    placeholder="e.g. standard, protection, IP"
                    style={{backgroundColor: 'inherit'}}
                  />
                  <p className="text-xs text-slate-500 dark:text-slate-400">Optional tags to help organize clauses</p>
                </div>
              </div>
            </div>

            <div className="flex justify-end gap-3 pt-2 border-t border-slate-100 dark:border-slate-700">
              <Button
                type="button"
                variant="outline"
                onClick={() => setCustomClauseModal(false)}
                className="h-8 border-slate-200 dark:border-slate-700 text-xs"
                style={{backgroundColor: 'inherit'}}
              >
                Cancel
              </Button>
              <Button
                type="button"
                disabled={!newCustomClause.title.trim() || !newCustomClause.content.trim()}
                onClick={() => {
                  if (!newCustomClause.title.trim() || !newCustomClause.content.trim()) return;
                  setData(d => ({
                    ...d,
                    customClauses: [
                      ...(d.customClauses || []),
                      {
                        title: newCustomClause.title.trim(),
                        content: newCustomClause.content.trim(),
                        category: newCustomClause.category,
                        tags: newCustomClause.tags.split(',').map(t => t.trim()).filter(Boolean),
                      },
                    ],
                  }));
                  setNewCustomClause({ title: '', content: '', category: 'General Provisions', tags: '' });
                  setCustomClauseModal(false);
                }}
                className="h-8 text-xs"
              >
                Add Clause
              </Button>
            </div>
          </div>
        </div>
      )}
      {touched && !isValid && (
        <div className="text-xs text-destructive mt-1">At least one library or custom clause is required.</div>
      )}
      <div className="sticky bottom-0 left-0 right-0 bg-gradient-to-t from-white via-white to-transparent dark:from-background dark:via-background dark:to-transparent pt-6 pb-4 mt-8 z-10">
        <div className="flex justify-between gap-2 max-w-xl">
          <Button
            type="button"
            variant="outline"
            className="h-9 px-4 shadow-sm transition-all hover:shadow bg-white dark:bg-slate-950 border-slate-200 dark:border-slate-700 text-xs"
            onClick={prevStep}
          >
            <ChevronLeft className="mr-1.5 h-3 w-3" />
            Back
          </Button>
          <Button
            type="button"
            className="flex items-center h-9 px-4 shadow-sm transition-all hover:shadow text-xs"
            onClick={() => {
              setTouched(true);
              if (isValid) nextStep();
              else nextStep(); // allow navigation but show validation if invalid
            }}
          >
            Continue
            <ChevronRight className="ml-1.5 h-3 w-3" />
          </Button>
        </div>
      </div>
    </form>
  );
};

export default Step4LegalClauses;