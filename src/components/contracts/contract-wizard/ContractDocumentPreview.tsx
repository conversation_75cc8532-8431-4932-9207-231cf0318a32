import React, { useRef, useState } from 'react';
import html2pdf from 'html2pdf.js';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import {
  Bold,
  Italic,
  Underline,
  AlignLeft,
  AlignCenter,
  AlignRight,
  List,
  ListOrdered,
  Heading1,
  Heading2,
  Download,
  Undo,
  Redo,
  Table
} from 'lucide-react';
import { useContractWizard } from './ContractWizardContext';
import { generateContractContent } from './contractUtils';

/**
 * ContractDocumentPreview: Presentational contract preview UI with editing capabilities
 * - Professional legal document styling
 * - Realistic contract formatting and layout
 * - Editing toolbar and functionality
 */
const ContractDocumentPreview: React.FC = () => {
  const { data } = useContractWizard();
  const previewRef = useRef<HTMLDivElement>(null);
  const editorRef = useRef<HTMLDivElement>(null);

  // Generate contract content
  const [content, setContent] = useState<string>(
    data.importedContent || generateContractContent(data)
  );

  // Update content when data changes
  React.useEffect(() => {
    // If we have imported content, use that instead of generating content
    if (data.importedContent) {
      setContent(data.importedContent);
    } else {
      setContent(generateContractContent(data));
    }
  }, [data, data.importedContent]);

  // Handle PDF export
  const handleExportPDF = () => {
    if (previewRef.current) {
      html2pdf().from(previewRef.current).set({
        margin: [0.75, 0.75, 0.75, 0.75],
        filename: `${data.title || 'contract'}.pdf`,
        html2canvas: { scale: 2 },
        jsPDF: { unit: 'in', format: 'a4', orientation: 'portrait' },
      }).save();
    }
  };



  // Handle editor command
  const handleCommand = (command: string, value: string = '') => {
    // Use modern approach instead of deprecated execCommand
    try {
      if (editorRef.current) {
        editorRef.current.focus();
        // For basic commands, we can use the Selection API
        const selection = window.getSelection();
        if (selection && selection.rangeCount > 0) {
          // This is a simplified implementation - in a real app you'd want to use a proper rich text editor
          console.warn(`Rich text editing commands are deprecated. Command: ${command}, Value: ${value}. Consider using a modern editor like TipTap or Quill.`);
        }
      }
    } catch (error) {
      console.error('Error executing editor command:', error);
    }
  };

  // Simple table function
  const insertTable = () => {
    // Fixed size tables for simplicity
    const rows = 3;
    const cols = 3;

    let tableHTML = '<table border="1" style="width:100%; border-collapse: collapse;">';

    // Create header row
    tableHTML += '<tr>';
    for (let j = 0; j < cols; j++) {
      tableHTML += '<th style="border: 1px solid #ccc; padding: 8px; background-color: #f2f2f2;">Header ' + (j + 1) + '</th>';
    }
    tableHTML += '</tr>';

    // Create data rows
    for (let i = 0; i < rows - 1; i++) {
      tableHTML += '<tr>';
      for (let j = 0; j < cols; j++) {
        tableHTML += '<td style="border: 1px solid #ccc; padding: 8px;">Cell ' + (i + 1) + '-' + (j + 1) + '</td>';
      }
      tableHTML += '</tr>';
    }

    tableHTML += '</table><p></p>';

    // Use modern approach instead of deprecated execCommand
    try {
      if (editorRef.current) {
        editorRef.current.focus();
        // For table insertion, we can use the Selection API
        const selection = window.getSelection();
        if (selection && selection.rangeCount > 0) {
          // This is a simplified implementation - in a real app you'd want to use a proper rich text editor
          console.warn('Rich text editing commands are deprecated. Consider using a modern editor like TipTap or Quill.');
        }
      }
    } catch (error) {
      console.error('Error inserting table:', error);
    }
  };





  return (
    <div className="w-full h-full flex flex-col overflow-hidden" style={{ height: '100%', minHeight: '80vh' }}>
      {/* Simple header with title and export button */}
      <div className="flex items-center justify-between py-2 px-4">
        <h2>{data.title || 'Contract Preview'}</h2>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleExportPDF}
            className="h-8"
          >
            <Download className="h-4 w-4 mr-2" />
            Export PDF
          </Button>
        </div>
      </div>

      {/* Enhanced toolbar with fonts, font size, and table */}
      <div className="flex items-center gap-1 py-1 px-4 border-y overflow-x-auto">
        {/* Font family selector */}
        <Select onValueChange={(value) => handleCommand('fontName', value)}>
          <SelectTrigger className="h-8 w-[130px] mr-1">
            <SelectValue placeholder="Font" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="Arial">Arial</SelectItem>
            <SelectItem value="Times New Roman">Times New Roman</SelectItem>
            <SelectItem value="Courier New">Courier New</SelectItem>
            <SelectItem value="Georgia">Georgia</SelectItem>
            <SelectItem value="Verdana">Verdana</SelectItem>
            <SelectItem value="Helvetica">Helvetica</SelectItem>
          </SelectContent>
        </Select>

        {/* Font size selector */}
        <Select onValueChange={(value) => handleCommand('fontSize', value)}>
          <SelectTrigger className="h-8 w-[80px] mr-1">
            <SelectValue placeholder="Size" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="1">8pt</SelectItem>
            <SelectItem value="2">10pt</SelectItem>
            <SelectItem value="3">12pt</SelectItem>
            <SelectItem value="4">14pt</SelectItem>
            <SelectItem value="5">18pt</SelectItem>
            <SelectItem value="6">24pt</SelectItem>
            <SelectItem value="7">36pt</SelectItem>
          </SelectContent>
        </Select>

        <span className="mx-1 border-l h-5" />

        {/* Text formatting */}
        <Button variant="ghost" size="sm" className="h-8" onClick={() => handleCommand('bold')}>
          <Bold className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm" className="h-8" onClick={() => handleCommand('italic')}>
          <Italic className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm" className="h-8" onClick={() => handleCommand('underline')}>
          <Underline className="h-4 w-4" />
        </Button>

        <span className="mx-1 border-l h-5" />

        {/* Alignment */}
        <Button variant="ghost" size="sm" className="h-8" onClick={() => handleCommand('justifyLeft')}>
          <AlignLeft className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm" className="h-8" onClick={() => handleCommand('justifyCenter')}>
          <AlignCenter className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm" className="h-8" onClick={() => handleCommand('justifyRight')}>
          <AlignRight className="h-4 w-4" />
        </Button>

        <span className="mx-1 border-l h-5" />

        {/* Lists */}
        <Button variant="ghost" size="sm" className="h-8" onClick={() => handleCommand('insertUnorderedList')}>
          <List className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm" className="h-8" onClick={() => handleCommand('insertOrderedList')}>
          <ListOrdered className="h-4 w-4" />
        </Button>

        <span className="mx-1 border-l h-5" />

        {/* Headings */}
        <Button variant="ghost" size="sm" className="h-8" onClick={() => handleCommand('formatBlock', '<h1>')}>
          <Heading1 className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm" className="h-8" onClick={() => handleCommand('formatBlock', '<h2>')}>
          <Heading2 className="h-4 w-4" />
        </Button>

        <span className="mx-1 border-l h-5" />

        {/* Table */}
        <Button variant="ghost" size="sm" className="h-8" onClick={() => insertTable()}>
          <Table className="h-4 w-4" />
        </Button>

        <span className="mx-1 border-l h-5" />

        {/* Undo/Redo */}
        <Button variant="ghost" size="sm" className="h-8" onClick={() => handleCommand('undo')}>
          <Undo className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm" className="h-8" onClick={() => handleCommand('redo')}>
          <Redo className="h-4 w-4" />
        </Button>
      </div>

      {/* Document Content - Plain text editor */}
      <div className="flex-1 overflow-auto p-4" style={{ maxHeight: 'calc(100vh - 100px)' }}>
        <div
          ref={editorRef}
          className="focus:outline-none scrollbar-thin"
          contentEditable
          dangerouslySetInnerHTML={{ __html: content }}
          style={{
            fontFamily: '"Times New Roman", serif',
            lineHeight: '1.5',
            color: '#111827',
            minHeight: '100%',
            padding: '1rem'
          }}
        />
        {/* Hidden preview div for PDF export */}
        <div className="hidden" ref={previewRef} dangerouslySetInnerHTML={{ __html: content }}></div>
      </div>
    </div>
  );
};

export default ContractDocumentPreview;
