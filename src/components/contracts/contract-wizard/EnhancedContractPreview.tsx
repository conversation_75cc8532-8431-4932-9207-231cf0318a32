import React, { useRef } from 'react';
// @ts-ignore
import html2pdf from 'html2pdf.js';
import { Button } from '@/components/ui/button';
import { useContractWizard } from './ContractWizardContext';

/**
 * EnhancedContractPreview: A professional-looking contract preview component
 * - Designed to look like a real legal document
 * - Proper spacing, formatting, and typography
 * - PDF export functionality
 */
const EnhancedContractPreview: React.FC = () => {
  const { data } = useContractWizard();
  const previewRef = useRef<HTMLDivElement>(null);
  
  // Generate a random contract number for display purposes
  const contractNumber = React.useMemo(() => {
    return `CONT-${Math.floor(10000 + Math.random() * 90000)}-${new Date().getFullYear()}`;
  }, []);

  const handleExportPDF = () => {
    if (previewRef.current) {
      html2pdf().from(previewRef.current).set({
        margin: [0.75, 0.75, 0.75, 0.75],
        filename: `${data.title || 'contract'}.pdf`,
        html2canvas: { scale: 2 },
        jsPDF: { unit: 'in', format: 'a4', orientation: 'portrait' },
      }).save();
    }
  };

  // Type assertion to avoid TypeScript errors
  const client = (data.parties?.[0] || {}) as any;
  const contractor = (data.parties?.[1] || {}) as any;
  const effectiveDate = data.effectiveDate || new Date().toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  // Format party information in a more formal way
  const renderPartyBlock = (party: any, type: 'first' | 'second') => {
    if (!party || Object.keys(party).length === 0) {
      return (
        <div className="mb-6">
          <p className="font-bold mb-1">{type === 'first' ? 'FIRST PARTY' : 'SECOND PARTY'}:</p>
          <p className="ml-8 mb-1">Name: ____________________________</p>
          <p className="ml-8 mb-1">Address: ____________________________</p>
          <p className="ml-8 mb-1">____________________________</p>
        </div>
      );
    }
    
    return (
      <div className="mb-6">
        <p className="font-bold mb-1">{type === 'first' ? 'FIRST PARTY' : 'SECOND PARTY'}:</p>
        <p className="ml-8 mb-1">Name: {party.name || '____________________________'}</p>
        <p className="ml-8 mb-1">Address: {party.address || '____________________________'}</p>
        {party.representative && (
          <p className="ml-8 mb-1">Representative: {party.representative}</p>
        )}
        {party.title && (
          <p className="ml-8 mb-1">Title: {party.title}</p>
        )}
        {party.registration && (
          <p className="ml-8 mb-1">Registration No.: {party.registration}</p>
        )}
      </div>
    );
  };

  // Format section with proper legal document styling
  const renderSection = (title: string, content: string | React.ReactNode, sectionNumber: number) => (
    <section className="mb-8">
      <h2 className="text-lg font-bold mb-4 uppercase tracking-wide">{sectionNumber}. {title}</h2>
      {typeof content === 'string' ? (
        <div className="legal-clause ml-4">
          {content.split('\n').map((paragraph, idx) => (
            <p key={idx} className="mb-3 text-justify">{paragraph}</p>
          ))}
        </div>
      ) : (
        <div className="legal-clause ml-4">{content}</div>
      )}
    </section>
  );

  // Format legal clauses with proper numbering and indentation
  const renderLegalClauses = () => {
    if (!data.standardClauses || !data.standardClauses.length) {
      return <p className="mb-3 text-justify">No clauses selected.</p>;
    }

    const clauseMap: Record<string, { title: string, content: string }> = {
      confidentiality: {
        title: 'Confidentiality',
        content: 'Each party shall maintain the confidentiality of all Confidential Information disclosed to it by the other party and shall not use such Confidential Information for any purpose other than as expressly permitted under this Agreement.'
      },
      limitation_of_liability: {
        title: 'Limitation of Liability',
        content: 'In no event shall either party be liable for any indirect, incidental, special, consequential, or punitive damages, including without limitation, loss of profits, data, use, goodwill, or other intangible losses.'
      },
      termination: {
        title: 'Termination',
        content: 'Either party may terminate this Agreement at any time without cause upon thirty (30) days\' prior written notice to the other party.'
      },
      force_majeure: {
        title: 'Force Majeure',
        content: 'Neither party shall be liable for any failure or delay in performance under this Agreement due to circumstances beyond its reasonable control, including acts of God, natural disasters, terrorism, riots, or war.'
      },
      dispute_resolution: {
        title: 'Dispute Resolution',
        content: 'Any dispute arising out of or relating to this Agreement shall be resolved by binding arbitration in accordance with the rules of the American Arbitration Association.'
      },
      intellectual_property: {
        title: 'Intellectual Property Rights',
        content: 'All intellectual property rights, including patents, trademarks, copyrights, and trade secrets, in and to the deliverables shall be owned exclusively by the Company.'
      },
      nonCompete: {
        title: 'Non-Compete',
        content: 'Parties agree not to compete for a specified period as outlined in this agreement.'
      },
      nonSolicitation: {
        title: 'Non-Solicitation',
        content: 'Parties agree not to solicit employees, contractors, or clients from each other during and after the term of this agreement.'
      },
      returnOfInfo: {
        title: 'Return of Information',
        content: 'Upon termination of this agreement, all confidential information must be returned or destroyed.'
      },
      jurisdictionSpecific: {
        title: 'Jurisdiction-Specific Provisions',
        content: 'This agreement is governed by the laws of the jurisdiction specified.'
      }
    };

    return (
      <>
        {data.standardClauses.map((clauseId: string, idx: number) => {
          const clause = clauseMap[clauseId] || { title: clauseId, content: '' };
          return (
            <div key={idx} className="mb-4">
              <p className="font-semibold mb-2">{idx + 1}.{idx + 1}. {clause.title}</p>
              <p className="ml-6 text-justify">{clause.content}</p>
            </div>
          );
        })}
      </>
    );
  };

  // Format deliverables with proper bullet points
  const renderDeliverables = () => {
    if (!data.deliverables || !data.deliverables.length) {
      return <p className="ml-6">No deliverables specified.</p>;
    }

    return (
      <ul className="list-disc ml-10 mb-4">
        {data.deliverables.map((item: string, idx: number) => (
          <li key={idx} className="mb-1">{item}</li>
        ))}
      </ul>
    );
  };

  // Define sections with proper legal document structure
  const sections = [
    { 
      title: 'RECITALS', 
      content: (
        <>
          <p className="mb-3 text-justify">WHEREAS, {data.description || '[Contract Description]'};</p>
          <p className="mb-3 text-justify">WHEREAS, the Parties wish to enter into this Agreement to define their respective rights and obligations;</p>
          <p className="mb-3 text-justify">NOW, THEREFORE, in consideration of the mutual covenants and agreements contained herein, and other good and valuable consideration, the receipt and sufficiency of which is hereby acknowledged, the Parties agree as follows:</p>
        </>
      ),
      number: 1
    },
    { 
      title: 'TERM AND TERMINATION', 
      content: `This Agreement shall commence on the Effective Date of ${effectiveDate} and continue for a period of ${data.duration || '___________'}, unless earlier terminated in accordance with the provisions set forth herein.`,
      number: 2
    },
    { 
      title: 'PAYMENT AND DELIVERABLES', 
      content: (
        <>
          <p className="mb-3 text-justify"><span className="font-semibold">2.1. Contract Value:</span> The total value of this Agreement is {data.contractValue || '___________'} {data.currency || ''}, payable as set forth herein.</p>
          <p className="mb-3 text-justify"><span className="font-semibold">2.2. Payment Terms:</span> {data.paymentTerms || 'Payment terms to be determined.'}</p>
          <p className="mb-3 text-justify"><span className="font-semibold">2.3. Payment Method:</span> {data.paymentMethod || 'Payment method to be determined.'}</p>
          <p className="mb-3 text-justify"><span className="font-semibold">2.4. Deliverables:</span></p>
          {renderDeliverables()}
        </>
      ),
      number: 3
    },
    { 
      title: 'LEGAL PROVISIONS', 
      content: renderLegalClauses(),
      number: 4
    }
  ];

  // Render signature blocks
  const renderSignatureBlocks = () => {
    return (
      <div className="flex flex-col md:flex-row justify-between mt-16 pt-8 border-t border-gray-300">
        <div className="w-full md:w-5/12 mb-8 md:mb-0">
          <p className="font-bold mb-1">FIRST PARTY:</p>
          <div className="legal-signature-line"></div>
          <p className="font-semibold">{client?.name || 'Name'}</p>
          {client?.title && <p>{client.title}</p>}
          <p className="mt-4">Date: _____________________</p>
        </div>
        
        <div className="w-full md:w-5/12">
          <p className="font-bold mb-1">SECOND PARTY:</p>
          <div className="legal-signature-line"></div>
          <p className="font-semibold">{contractor?.name || 'Name'}</p>
          {contractor?.title && <p>{contractor.title}</p>}
          <p className="mt-4">Date: _____________________</p>
        </div>
      </div>
    );
  };

  return (
    <div className="relative mx-auto my-8 min-h-[80vh] max-h-[90vh] overflow-y-auto">
      {/* PDF Download Button */}
      <Button onClick={handleExportPDF} className="absolute right-4 top-4 z-20" variant="outline" size="sm">
        Download PDF
      </Button>
      
      {/* Legal Document */}
      <div className="legal-document-page legal-document" ref={previewRef}>
        {/* Watermark for draft */}
        <div className="legal-document-watermark">DRAFT</div>
        
        {/* Document Number */}
        <div className="legal-document-number">Contract No.: {contractNumber}</div>
        
        {/* Title */}
        <div className="text-center mb-10">
          <h1 className="text-2xl font-bold uppercase mb-1 tracking-widest">{data.title || 'CONTRACT AGREEMENT'}</h1>
          <p className="text-sm text-gray-600 mb-1">{data.contractType || 'Agreement'}</p>
          <p className="text-xs text-gray-500">Effective Date: {effectiveDate}</p>
        </div>
        
        {/* Parties */}
        <div className="mb-8">
          <p className="mb-4">THIS AGREEMENT is made and entered into as of the Effective Date by and between:</p>
          
          {renderPartyBlock(client, 'first')}
          
          <p className="mb-2 font-semibold text-center">AND</p>
          
          {renderPartyBlock(contractor, 'second')}
          
          <p className="mb-4 mt-6">
            {data.parties && data.parties.length > 1 
              ? `The First Party and Second Party may be referred to individually as a "Party" and collectively as the "Parties".` 
              : `The First Party shall be referred to as the "Party" in this agreement.`}
          </p>
        </div>
        
        {/* Horizontal Rule */}
        <hr className="border-t border-gray-300 my-8" />
        
        {/* Sections */}
        {sections.map((section, i) => (
          <React.Fragment key={section.title + '-' + i}>
            {renderSection(section.title, section.content, section.number)}
          </React.Fragment>
        ))}
        
        {/* Signature Blocks */}
        {renderSignatureBlocks()}
        
        {/* Page Number */}
        <div className="legal-page-number">Page 1 of 1</div>
      </div>
    </div>
  );
};

export default EnhancedContractPreview;
