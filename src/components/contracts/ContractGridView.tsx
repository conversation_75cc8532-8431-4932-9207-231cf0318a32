import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  CardContent,
  CardFooter
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import EnhancedGrid, { type GridItemProps } from "@/components/ui/enhanced-grid";
import { useGridState } from "@/hooks/useGridState";
import {
  Eye,
  FileText,
  MoreHorizontal,
  Download,
  Copy,
  Trash2,
  FileEdit,
  ExternalLink,
  Mail,
  Loader2
} from "lucide-react";
import { useWorkspace } from "@/lib/workspace-provider";
import { useClerkWorkspace } from "@/lib/clerk-workspace-provider";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { toast } from "@/components/ui/use-toast";
import { useApi } from "@/lib/api";
import { ContractService } from "@/services/api-services";
import { cn } from "@/lib/utils";
import DocumentExportButton from "./DocumentExportButton";
import type { Contract as ApiContract } from "@/services/api-types";

// Local interface for contracts with UI-specific properties
interface Contract {
  id: string;
  title: string;
  type: string;
  status: "draft" | "review" | "active" | "expired" | "rejected" | "pending_approval" | "terminated";
  createdBy: {
    name: string;
    avatar?: string;
    initials: string;
    id?: string;
  };
  createdDate: string;
  expiryDate?: string;
  counterparty: string;
  value?: string;
  workspaceId: string; // Workspace ID to associate contracts with workspaces
}

interface ContractGridViewProps {
  contracts: ApiContract[];
  onContractSelect: (contractId: string) => void;
  onPreviewContract?: (contractId: string) => void;
  selectedContracts?: string[];
  onSelectContract?: (contractId: string, isChecked: boolean) => void;
  onSelectAll?: (isChecked: boolean) => void;
  renderStatusBadge?: (status: string) => React.ReactNode;
}

const ContractGridView = ({
  contracts: apiContracts,
  onContractSelect,
  onPreviewContract,
  selectedContracts = [],
  onSelectContract,
  onSelectAll,
  renderStatusBadge,
}: ContractGridViewProps) => {
  const { fetch } = useApi();
  const [deleteLoading, setDeleteLoading] = useState(false);
  const { currentWorkspace } = useClerkWorkspace();

  // Map API contracts to UI contracts
  const contracts: Contract[] = apiContracts.map((contract: ApiContract) => ({
    id: contract.id,
    title: contract.title,
    type: contract.type,
    status: contract.status,
    createdBy: {
      name: contract.created_by?.name || 'Unknown',
      id: contract.created_by?.id || 'unknown-id',
      // Generate initials from name if not provided
      initials: contract.created_by?.name
        ? contract.created_by.name
            .split(' ')
            .map(n => n[0])
            .join('')
            .toUpperCase()
            .substring(0, 2)
        : 'UN',
      // Generate avatar from name
      avatar: contract.created_by?.name
        ? `https://api.dicebear.com/7.x/avataaars/svg?seed=${contract.created_by.name.toLowerCase().replace(/\s/g, '')}`
        : undefined
    },
    createdDate: contract.created_at,
    expiryDate: contract.expiry_date,
    counterparty: contract.counterparty || 'N/A',
    value: contract.value,
    workspaceId: contract.workspace_id
  }));

  const getStatusBadge = (status: Contract["status"]) => {
    switch (status) {
      case "draft":
        return <Badge variant="outline">Draft</Badge>;
      case "review":
        return <Badge variant="secondary">In Review</Badge>;
      case "active":
        return <Badge variant="default">Active</Badge>;
      case "expired":
        return <Badge variant="destructive">Expired</Badge>;
      case "rejected":
        return <Badge variant="destructive">Rejected</Badge>;
      default:
        return null;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Helper function to simplify contract titles
  const simplifyTitle = (title: string) => {
    // If title is too long, truncate it
    if (title.length > 40) {
      return title.substring(0, 40) + "...";
    }
    return title;
  };

  // Enhanced grid state management
  const gridState = useGridState({
    data: contracts,
    getItemId: (contract: Contract) => contract.id,
    defaultSelected: selectedContracts,
  });

  // Selection handlers
  const handleItemSelect = (contractId: string, selected: boolean) => {
    gridState.handleItemSelect(contractId, selected);
    if (onSelectContract) {
      onSelectContract(contractId, selected);
    }
  };

  const handleSelectAll = (selected: boolean) => {
    gridState.handleSelectAll(selected);
    if (onSelectAll) {
      onSelectAll(selected);
    }
  };

  // Card render function for enhanced grid
  const renderContractCard = ({ item: contract, isSelected, onSelect }: GridItemProps<Contract>) => (
    <Card
      className={cn(
        "overflow-hidden hover:shadow-md transition-all duration-200 cursor-pointer",
        isSelected && "ring-2 ring-primary",
        "table-row-actions" // For hover effects
      )}
      onClick={() => onContractSelect(contract.id)}
    >
      <div className="relative p-3 sm:p-4 border-b">
        {onSelectContract && onSelect && (
          <div className="absolute top-3 sm:top-4 left-3 sm:left-4">
            <Checkbox
              checked={isSelected}
              onCheckedChange={(checked) => onSelect(!!checked)}
              onClick={(e) => e.stopPropagation()}
            />
          </div>
        )}
        <div className="flex items-center mb-2 ml-7">
          <div className="h-7 w-7 sm:h-8 sm:w-8 rounded-full bg-muted flex items-center justify-center text-xs mr-2">
            {contract.createdBy.initials}
          </div>
          <div className="text-sm font-medium text-foreground line-clamp-2">
            {simplifyTitle(contract.title)}
          </div>
        </div>
        <div className="flex items-center justify-between mt-3 flex-wrap gap-2">
          <Badge variant="outline" className="text-xs">{contract.type}</Badge>
          {renderStatusBadge ? renderStatusBadge(contract.status) : getStatusBadge(contract.status)}
        </div>
      </div>
      <CardContent className="p-3 sm:p-4">
        <div className="text-sm mb-2 line-clamp-1">
          <span className="text-muted-foreground">Counterparty:</span> {contract.counterparty}
        </div>
        <div className="grid grid-cols-2 gap-2 text-xs">
          <div>
            <span className="text-muted-foreground">Created:</span><br />
            {formatDate(contract.createdDate)}
          </div>
          <div>
            <span className="text-muted-foreground">Expires:</span><br />
            {contract.expiryDate ? formatDate(contract.expiryDate) : "-"}
          </div>
        </div>
        {contract.value && (
          <div className="text-sm font-medium mt-3 border-t pt-3">
            <span className="text-muted-foreground">Value:</span> {contract.value}
          </div>
        )}
      </CardContent>
      <CardFooter className="p-3 sm:p-4 pt-0 flex items-center justify-between border-t mt-2 flex-wrap gap-2">
        <div className="flex items-center">
          <span className="text-xs text-muted-foreground truncate max-w-[120px] sm:max-w-none">
            By: {contract.createdBy.name}
          </span>
        </div>
        <div className="flex items-center table-row-actions">
          {onPreviewContract && (
            <Button
              variant="ghost"
              size="icon"
              className="h-7 w-7 sm:h-8 sm:w-8 hover:bg-muted"
              onClick={(e) => {
                e.stopPropagation();
                onPreviewContract(contract.id);
              }}
              title="Quick Preview"
            >
              <Eye className="h-3.5 w-3.5 sm:h-4 sm:w-4" />
            </Button>
          )}
          <DocumentExportButton
            contractId={contract.id}
            contractTitle={contract.title}
            variant="ghost"
            size="sm"
            showLabel={false}
            className="h-7 w-7 sm:h-8 sm:w-8"
            enableTemplateSelection={true}
            workspaceId={currentWorkspace?.id}
          />
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="h-7 w-7 sm:h-8 sm:w-8 hover:bg-muted"
                onClick={(e) => e.stopPropagation()}
              >
                <MoreHorizontal className="h-3.5 w-3.5 sm:h-4 sm:w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onContractSelect(contract.id)}>
                <ExternalLink className="mr-2 h-4 w-4" /> Open
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleEditContract(contract.id)}>
                <FileEdit className="mr-2 h-4 w-4" /> Edit
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleDuplicateContract(contract.id)}>
                <Copy className="mr-2 h-4 w-4" /> Duplicate
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleEmailContract(contract.id)}>
                <Mail className="mr-2 h-4 w-4" /> Email
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                className="text-destructive"
                onClick={() => handleDeleteClick(contract.id)}
                disabled={deleteLoading}
              >
                {deleteLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Deleting...
                  </>
                ) : (
                  <>
                    <Trash2 className="mr-2 h-4 w-4" /> Delete
                  </>
                )}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardFooter>
    </Card>
  );

  // Handler functions for contract actions
  const handleEditContract = (contractId: string) => {
    // Find the contract to edit
    const contract = contracts.find(c => c.id === contractId);
    if (!contract) return;

    // In a real app, this would navigate to the edit page or open an edit modal
    toast({
      title: "Editing Contract",
      description: `Now editing: ${contract.title}`,
    });

    // For demo purposes, we'll just call onContractSelect to open the contract
    onContractSelect(contractId);
  };

  const handleDuplicateContract = (contractId: string) => {
    // Find the contract to duplicate
    const contract = contracts.find(c => c.id === contractId);
    if (!contract) return;

    toast({
      title: "Contract Duplicated",
      description: `Created a copy of: ${contract.title}`,
    });
  };

  const handleDownloadContract = (contractId: string) => {
    // Find the contract to download
    const contract = contracts.find(c => c.id === contractId);
    if (!contract) return;

    // In a real app, this would trigger a download of the contract
    toast({
      title: "Downloading Contract",
      description: `Preparing ${contract.title} for download...`,
    });

    // Simulate download completion after a delay
    setTimeout(() => {
      toast({
        title: "Download Complete",
        description: `${contract.title} has been downloaded.`,
      });
    }, 2000);
  };

  const handleEmailContract = (contractId: string) => {
    // Find the contract to email
    const contract = contracts.find(c => c.id === contractId);
    if (!contract) return;

    toast({
      title: "Email Contract",
      description: `Preparing to email: ${contract.title}`,
    });
  };

  const handleDeleteClick = async (contractId: string) => {
    // Find the contract to delete
    const contract = contracts.find(c => c.id === contractId);
    if (!contract) return;

    setDeleteLoading(true);

    try {
      // Call the API to delete the contract
      await fetch(
        () => ContractService.deleteContract(contractId),
        "Deleting contract...",
        "Failed to delete contract"
      );

      toast({
        title: "Contract Deleted",
        description: `${contract.title} has been deleted.`,
        variant: "destructive",
      });

      // In a real implementation, we would refresh the contracts list
      // or notify the parent component to remove this contract

    } catch (err) {
      console.error("Error deleting contract:", err);
    } finally {
      setDeleteLoading(false);
    }
  };

  return (
    <div className="w-full">
      <EnhancedGrid
        data={gridState.filteredData}
        renderItem={renderContractCard}
        selectedItems={onSelectContract ? gridState.selectedItems : []}
        onItemSelect={onSelectContract ? handleItemSelect : undefined}
        onSelectAll={onSelectAll ? handleSelectAll : undefined}
        onItemClick={(contract: Contract) => onContractSelect(contract.id)}
        columns={{ default: 1, sm: 2, lg: 3 }}
        gap="gap-3 sm:gap-4"
        keyboardNavigation={true}
        selectionMode={onSelectContract ? "multiple" : "none"}
        showSelectAll={!!onSelectAll}
        emptyMessage="No contracts found"
        className="w-full"
      />
    </div>
  );
};

export default ContractGridView;
