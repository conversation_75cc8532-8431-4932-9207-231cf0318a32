import React, { useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Textarea } from '@/components/ui/textarea';
import { Upload, FileText, AlertCircle, Check, Loader2 } from 'lucide-react';
import { documentParser } from '@/services/documentParser';
import { contractExtractor } from '@/services/contractExtractor';

interface ImportDocumentModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const ImportDocumentModal: React.FC<ImportDocumentModalProps> = ({
  open,
  onOpenChange,
}) => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<string>('upload');
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [extractedText, setExtractedText] = useState<string>('');
  const [extractedData, setExtractedData] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const [pasteText, setPasteText] = useState<string>('');
  const [success, setSuccess] = useState<boolean>(false);
  
  // Refs
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  // Handle file upload
  const handleFileUpload = async (files: FileList | null) => {
    if (!files || files.length === 0) return;
    
    const file = files[0];
    setIsProcessing(true);
    setError(null);
    
    try {
      // Parse document to extract text
      const parsedDocument = await documentParser.parseDocument(file);
      setExtractedText(parsedDocument.text);
      
      // Extract contract data
      const extractedContract = await contractExtractor.extractFromFile(file);
      setExtractedData(extractedContract);
      
      // Show success message
      setSuccess(true);
      
      // Automatically proceed to wizard after a short delay
      setTimeout(() => {
        handleProceedToWizard();
      }, 1500);
    } catch (err) {
      console.error('Error extracting text:', err);
      setError('Failed to extract text from the document. Please try a different file format.');
    } finally {
      setIsProcessing(false);
    }
  };
  
  // Handle text paste
  const handleTextPaste = async () => {
    if (!pasteText.trim()) return;
    
    setIsProcessing(true);
    setError(null);
    
    try {
      // Extract text directly
      setExtractedText(pasteText);
      
      // Extract contract data from text
      const extractedContract = contractExtractor.extractFromText(pasteText);
      setExtractedData(extractedContract);
      
      // Show success message
      setSuccess(true);
      
      // Automatically proceed to wizard after a short delay
      setTimeout(() => {
        handleProceedToWizard();
      }, 1500);
    } catch (err) {
      console.error('Error processing text:', err);
      setError('Failed to process the text. Please check the format and try again.');
    } finally {
      setIsProcessing(false);
    }
  };
  
  // Handle drag events
  const handleDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };
  
  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };
  
  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };
  
  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
    handleFileUpload(e.dataTransfer.files);
  };
  
  // Handle file input change
  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleFileUpload(e.target.files);
  };
  
  // Proceed to contract wizard with extracted data
  const handleProceedToWizard = () => {
    // Store extracted data in session storage to pass to wizard
    if (extractedData) {
      sessionStorage.setItem('importedContractData', JSON.stringify({
        title: extractedData.metadata?.title || 'Imported Contract',
        content: extractedText,
        parties: extractedData.parties || [],
        effectiveDate: extractedData.metadata?.effectiveDate,
        type: extractedData.metadata?.type || 'agreement',
        importedData: true
      }));
      
      // Navigate to wizard with import flag
      navigate('/contracts/wizard?imported=true');
      
      // Close modal
      onOpenChange(false);
    }
  };
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Import Document</DialogTitle>
          <DialogDescription>
            Upload an existing document or paste text to create a new contract
          </DialogDescription>
        </DialogHeader>
        
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="upload">Upload File</TabsTrigger>
            <TabsTrigger value="paste">Paste Text</TabsTrigger>
          </TabsList>
          
          {/* Upload File Tab */}
          <TabsContent value="upload">
            <div className="space-y-4">
              <div
                className={`border-2 ${isDragging ? 'border-primary' : 'border-dashed'} rounded-lg p-8 text-center cursor-pointer transition-colors`}
                onDragEnter={handleDragEnter}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
                onClick={() => fileInputRef.current?.click()}
              >
                <input
                  type="file"
                  ref={fileInputRef}
                  onChange={handleFileInputChange}
                  className="hidden"
                  accept=".pdf,.doc,.docx,.txt,.html"
                />
                <Upload className={`h-10 w-10 mx-auto mb-3 ${isDragging ? 'text-primary' : 'text-muted-foreground'}`} />
                <h3 className="text-lg font-medium mb-1">
                  {isDragging ? 'Drop file here' : 'Drag & drop file here'}
                </h3>
                <p className="text-sm text-muted-foreground mb-4">
                  or click to browse
                </p>
                <div className="text-xs text-muted-foreground">
                  Supported formats: PDF, DOCX, TXT, HTML
                </div>
              </div>
            </div>
          </TabsContent>
          
          {/* Paste Text Tab */}
          <TabsContent value="paste">
            <div className="space-y-4">
              <div>
                <Textarea
                  placeholder="Paste contract text here..."
                  className="min-h-[200px]"
                  value={pasteText}
                  onChange={(e) => setPasteText(e.target.value)}
                />
              </div>
              
              <Button 
                onClick={handleTextPaste}
                disabled={isProcessing || !pasteText.trim()}
                className="w-full"
              >
                {isProcessing ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>
                    <FileText className="mr-2 h-4 w-4" />
                    Extract Text
                  </>
                )}
              </Button>
            </div>
          </TabsContent>
        </Tabs>
        
        {/* Status Messages */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4 mr-2" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        
        {success && (
          <Alert className="bg-green-50 text-green-800 border-green-200">
            <Check className="h-4 w-4 mr-2 text-green-600" />
            <AlertDescription>
              Document processed successfully! Redirecting to contract wizard...
            </AlertDescription>
          </Alert>
        )}
        
        {isProcessing && (
          <div className="flex items-center justify-center py-4">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="ml-2 text-sm">Processing document...</span>
          </div>
        )}
        
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button 
            onClick={handleProceedToWizard} 
            disabled={isProcessing || !extractedText || !extractedData}
          >
            Continue to Wizard
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ImportDocumentModal;
