import React, { useState } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON><PERSON>,
  Card<PERSON>itle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { FileText, Plus, Search, Tag, MoreHorizontal } from "lucide-react";

interface Clause {
  id: string;
  title: string;
  content: string;
  category: string;
  tags: string[];
  lastUsed?: string;
  usageCount?: number;
}

interface ClauseLibraryProps {
  onSelectClause?: (clause: Clause) => void;
}

const ClauseLibrary = ({ onSelectClause }: ClauseLibraryProps) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [isAddingClause, setIsAddingClause] = useState(false);
  const [newClause, setNewClause] = useState({
    title: "",
    content: "",
    category: "general",
    tags: "",
  });

  // Mock data for clauses
  const clauses: Clause[] = [
    {
      id: "c1",
      title: "Standard Confidentiality",
      content:
        "Each party shall maintain the confidentiality of all Confidential Information disclosed to it by the other party and shall not use such Confidential Information for any purpose other than as expressly permitted under this Agreement.",
      category: "confidentiality",
      tags: ["standard", "protection"],
      lastUsed: "2023-06-10",
      usageCount: 42,
    },
    {
      id: "c2",
      title: "Intellectual Property Rights",
      content:
        "All intellectual property rights, including patents, trademarks, copyrights, and trade secrets, in and to the deliverables shall be owned exclusively by the Company.",
      category: "intellectual_property",
      tags: ["ownership", "rights"],
      lastUsed: "2023-06-15",
      usageCount: 38,
    },
    {
      id: "c3",
      title: "Limitation of Liability",
      content:
        "In no event shall either party be liable for any indirect, incidental, special, consequential, or punitive damages, including without limitation, loss of profits, data, use, goodwill, or other intangible losses.",
      category: "liability",
      tags: ["limitation", "damages"],
      lastUsed: "2023-06-12",
      usageCount: 56,
    },
    {
      id: "c4",
      title: "Termination for Convenience",
      content:
        "Either party may terminate this Agreement at any time without cause upon thirty (30) days' prior written notice to the other party.",
      category: "termination",
      tags: ["convenience", "notice"],
      lastUsed: "2023-06-08",
      usageCount: 29,
    },
    {
      id: "c5",
      title: "Force Majeure",
      content:
        "Neither party shall be liable for any failure or delay in performance under this Agreement due to circumstances beyond its reasonable control, including acts of God, natural disasters, terrorism, riots, or war.",
      category: "general",
      tags: ["force majeure", "delay"],
      lastUsed: "2023-06-05",
      usageCount: 47,
    },
  ];

  // Categories for clauses
  const categories = [
    { id: "confidentiality", name: "Confidentiality" },
    { id: "intellectual_property", name: "Intellectual Property" },
    { id: "liability", name: "Liability" },
    { id: "termination", name: "Termination" },
    { id: "payment", name: "Payment Terms" },
    { id: "general", name: "General Provisions" },
  ];

  const handleAddClause = () => {
    setIsAddingClause(true);
  };

  const handleClauseInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const { name, value } = e.target;
    setNewClause({ ...newClause, [name]: value });
  };

  const handleCategoryChange = (value: string) => {
    setNewClause({ ...newClause, category: value });
  };

  const handleSaveClause = () => {
    // In a real app, this would save the clause to the backend
    console.log("Saving clause:", newClause);
    setIsAddingClause(false);
    setNewClause({
      title: "",
      content: "",
      category: "general",
      tags: "",
    });
  };

  // Filter clauses based on search and category
  const filteredClauses = clauses.filter((clause) => {
    const matchesSearch =
      searchQuery === "" ||
      clause.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      clause.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
      clause.tags.some((tag) =>
        tag.toLowerCase().includes(searchQuery.toLowerCase()),
      );

    const matchesCategory =
      selectedCategory === null || clause.category === selectedCategory;

    return matchesSearch && matchesCategory;
  });

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">Clause Library</h3>
        <Button size="sm" onClick={handleAddClause}>
          <Plus className="h-4 w-4 mr-2" />
          Add Clause
        </Button>
      </div>

      <div className="flex flex-col md:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search clauses..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Select
          value={selectedCategory || ""}
          onValueChange={(value) =>
            setSelectedCategory(value === "" ? null : value)
          }
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="All Categories" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            {categories.map((cat) => (
              <SelectItem key={cat.id} value={cat.id}>{cat.name}</SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2 max-h-[400px] overflow-y-auto pr-2">
        {filteredClauses.length > 0 ? (
          filteredClauses.map((clause) => (
            <Card key={clause.id} className="cursor-pointer hover:bg-muted/50">
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-base">{clause.title}</CardTitle>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem
                        onClick={() => onSelectClause && onSelectClause(clause)}
                      >
                        Insert into Contract
                      </DropdownMenuItem>
                      <DropdownMenuItem>Edit</DropdownMenuItem>
                      <DropdownMenuItem>Duplicate</DropdownMenuItem>
                      <DropdownMenuItem className="text-destructive">
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline">
                    {categories.find((c) => c.id === clause.category)?.name ||
                      clause.category}
                  </Badge>
                  {clause.tags.map((tag) => (
                    <Badge
                      key={tag}
                      variant="secondary"
                      className="text-xs bg-muted"
                    >
                      {tag}
                    </Badge>
                  ))}
                </div>
              </CardHeader>
              <CardContent className="pb-3">
                <p className="text-sm text-muted-foreground line-clamp-2">
                  {clause.content}
                </p>
              </CardContent>
              <div className="px-6 pb-3 flex items-center justify-between text-xs text-muted-foreground">
                <span>Used {clause.usageCount} times</span>
                <span>Last used: {clause.lastUsed}</span>
              </div>
            </Card>
          ))
        ) : (
          <div className="text-center py-8">
            <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
            <h3 className="text-lg font-medium">No clauses found</h3>
            <p className="text-muted-foreground">
              {searchQuery || selectedCategory
                ? "Try adjusting your search or filters"
                : "Add clauses to your library to get started"}
            </p>
          </div>
        )}
      </div>

      <Dialog open={isAddingClause} onOpenChange={setIsAddingClause}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Add New Clause</DialogTitle>
            <DialogDescription>
              Create a new clause to add to your library for reuse in contracts.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="title">Clause Title</Label>
              <Input
                id="title"
                name="title"
                value={newClause.title}
                onChange={handleClauseInputChange}
                placeholder="Enter a descriptive title"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="content">Clause Content</Label>
              <Textarea
                id="content"
                name="content"
                value={newClause.content}
                onChange={handleClauseInputChange}
                placeholder="Enter the clause text"
                rows={6}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="category">Category</Label>
                <Select
                  value={newClause.category}
                  onValueChange={handleCategoryChange}
                >
                  <SelectTrigger id="category">
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="tags">Tags (comma separated)</Label>
                <div className="relative">
                  <Tag className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="tags"
                    name="tags"
                    value={newClause.tags}
                    onChange={handleClauseInputChange}
                    placeholder="standard, protection, etc."
                    className="pl-8"
                  />
                </div>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddingClause(false)}>
              Cancel
            </Button>
            <Button onClick={handleSaveClause}>Save Clause</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ClauseLibrary;
