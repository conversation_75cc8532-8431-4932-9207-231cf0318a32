import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Download,
  FileText,
  FileType,
  Mail,
  Printer,
  Share2,
} from "lucide-react";

interface ContractExportProps {
  contractId: string;
  contractTitle?: string;
}

const ContractExport = ({
  contractId,
  contractTitle = "Service Agreement",
}: ContractExportProps) => {
  const [exportFormat, setExportFormat] = useState("pdf");
  const [includeAttachments, setIncludeAttachments] = useState(true);
  const [includeVersionHistory, setIncludeVersionHistory] = useState(false);
  const [includeComments, setIncludeComments] = useState(false);
  const [includeMetadata, setIncludeMetadata] = useState(true);

  const handleExport = () => {
    // In a real app, this would trigger the export process
    console.log("Exporting contract:", {
      contractId,
      format: exportFormat,
      options: {
        includeAttachments,
        includeVersionHistory,
        includeComments,
        includeMetadata,
      },
    });
  };

  const handlePrint = () => {
    // In a real app, this would open the print dialog
    console.log("Printing contract:", contractId);
  };

  const handleShare = () => {
    // In a real app, this would open a sharing dialog
    console.log("Sharing contract:", contractId);
  };

  const handleEmail = () => {
    // In a real app, this would open an email dialog
    console.log("Emailing contract:", contractId);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Export Contract</CardTitle>
        <CardDescription>
          Export "{contractTitle}" in various formats
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-3">
          <Label>Export Format</Label>
          <RadioGroup
            value={exportFormat}
            onValueChange={setExportFormat}
            className="flex flex-col space-y-1"
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="pdf" id="pdf" />
              <Label htmlFor="pdf" className="flex items-center cursor-pointer">
                <FileText className="h-4 w-4 mr-2 text-red-500" />
                PDF Document (.pdf)
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="docx" id="docx" />
              <Label
                htmlFor="docx"
                className="flex items-center cursor-pointer"
              >
                <FileText className="h-4 w-4 mr-2 text-blue-500" />
                Word Document (.docx)
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="txt" id="txt" />
              <Label htmlFor="txt" className="flex items-center cursor-pointer">
                <FileType className="h-4 w-4 mr-2 text-gray-500" />
                Plain Text (.txt)
              </Label>
            </div>
          </RadioGroup>
        </div>

        <Separator />

        <div className="space-y-3">
          <Label>Export Options</Label>
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="attachments"
                checked={includeAttachments}
                onCheckedChange={(checked) =>
                  setIncludeAttachments(checked as boolean)
                }
              />
              <Label htmlFor="attachments" className="text-sm cursor-pointer">
                Include attachments
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="version-history"
                checked={includeVersionHistory}
                onCheckedChange={(checked) =>
                  setIncludeVersionHistory(checked as boolean)
                }
              />
              <Label
                htmlFor="version-history"
                className="text-sm cursor-pointer"
              >
                Include version history
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="comments"
                checked={includeComments}
                onCheckedChange={(checked) =>
                  setIncludeComments(checked as boolean)
                }
              />
              <Label htmlFor="comments" className="text-sm cursor-pointer">
                Include comments
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="metadata"
                checked={includeMetadata}
                onCheckedChange={(checked) =>
                  setIncludeMetadata(checked as boolean)
                }
              />
              <Label htmlFor="metadata" className="text-sm cursor-pointer">
                Include metadata (creation date, parties, etc.)
              </Label>
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex flex-col space-y-4">
        <Button
          className="w-full flex items-center justify-center"
          onClick={handleExport}
        >
          <Download className="mr-2 h-4 w-4" />
          Export {exportFormat.toUpperCase()}
        </Button>

        <div className="flex justify-between w-full">
          <Button variant="outline" size="sm" onClick={handlePrint}>
            <Printer className="mr-2 h-4 w-4" />
            Print
          </Button>
          <Button variant="outline" size="sm" onClick={handleEmail}>
            <Mail className="mr-2 h-4 w-4" />
            Email
          </Button>
          <Button variant="outline" size="sm" onClick={handleShare}>
            <Share2 className="mr-2 h-4 w-4" />
            Share
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
};

export default ContractExport;
