import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import ContractReviewPreview from './ContractReviewPreview';
import { useApi } from '@/lib/api';
import { ContractService } from '@/services/api-services';
import { Loader2 } from 'lucide-react';

interface ContractPreviewModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  contractId: string | null;
}

const ContractPreviewModal: React.FC<ContractPreviewModalProps> = ({
  open,
  onOpenChange,
  contractId
}) => {
  const { fetch } = useApi();
  const [contractTitle, setContractTitle] = useState<string>("Contract Preview");
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch contract details when the modal opens
  useEffect(() => {
    if (open && contractId) {
      setLoading(true);
      setError(null);

      const fetchContract = async () => {
        try {
          const result = await fetch(
            () => ContractService.getContract(contractId),
            "Loading contract...",
            "Failed to load contract"
          );

          if (result) {
            setContractTitle(result.title);
          }
        } catch (err) {
          console.error("Error fetching contract:", err);
          setError("Failed to load contract details");
        } finally {
          setLoading(false);
        }
      };

      fetchContract();
    }
  }, [open, contractId, fetch]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl h-[90vh] p-0">
        <DialogHeader className="px-6 py-4 border-b">
          <DialogTitle>Contract Preview</DialogTitle>
        </DialogHeader>
        <div className="h-[calc(90vh-4rem)]">
          {loading ? (
            <div className="flex flex-col items-center justify-center h-full">
              <Loader2 className="h-8 w-8 text-primary animate-spin mb-4" />
              <p className="text-muted-foreground">Loading contract...</p>
            </div>
          ) : error ? (
            <div className="flex flex-col items-center justify-center h-full">
              <p className="text-destructive mb-2">Error: {error}</p>
            </div>
          ) : contractId ? (
            <ContractReviewPreview
              contractId={contractId}
              contractTitle={contractTitle}
              readOnly={true}
            />
          ) : null}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ContractPreviewModal;
