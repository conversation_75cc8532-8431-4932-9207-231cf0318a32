import React, { useRef, useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import {
  Bold,
  Italic,
  Underline,
  AlignLeft,
  AlignCenter,
  AlignRight,
  List,
  ListOrdered,
  Heading1,
  Heading2,
  Save,
  Undo,
  Redo,
  Edit,
  Eye,
  Download
} from "lucide-react";

interface ContractReviewPreviewProps {
  contractId?: string;
  contractTitle?: string;
  contractContent?: string;
  readOnly?: boolean;
  onSave?: (content: string) => void;
}

const ContractReviewPreview: React.FC<ContractReviewPreviewProps> = ({
  contractId = "contract-123",
  contractTitle = "Service Agreement",
  contractContent,
  readOnly = false,
  onSave,
}) => {
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [content, setContent] = useState<string>(
    contractContent ||
    `<h1 style="text-align: center; margin-bottom: 24px;">SERVICE AGREEMENT</h1>
    <p style="text-align: center; margin-bottom: 36px;"><strong>Contract #: CONT-${Math.floor(10000 + Math.random() * 90000)}</strong></p>

    <div style="display: flex; justify-content: space-between; margin-bottom: 36px;">
      <div style="width: 45%;">
        <p><strong>CLIENT:</strong></p>
        <p>Acme Corporation</p>
        <p>123 Business Ave</p>
        <p>New York, NY 10001</p>
        <p>Representative: John Smith</p>
      </div>
      <div style="width: 45%;">
        <p><strong>SERVICE PROVIDER:</strong></p>
        <p>Tech Solutions Inc.</p>
        <p>456 Tech Blvd</p>
        <p>San Francisco, CA 94105</p>
        <p>Representative: Jane Doe</p>
      </div>
    </div>

    <p style="text-align: center; margin-bottom: 24px;"><strong>EFFECTIVE DATE: ${new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}</strong></p>

    <h2 style="margin-top: 36px; margin-bottom: 12px;">1. SERVICES</h2>
    <p style="margin-bottom: 24px;">The Service Provider agrees to provide the following services to the Client:</p>
    <ul style="margin-bottom: 24px;">
      <li>Software development and implementation</li>
      <li>Technical support and maintenance</li>
      <li>User training and documentation</li>
      <li>System integration services</li>
    </ul>

    <h2 style="margin-top: 36px; margin-bottom: 12px;">2. TERM</h2>
    <p style="margin-bottom: 24px;">This Agreement shall commence on the Effective Date and continue for a period of 12 months, unless terminated earlier in accordance with the provisions of this Agreement.</p>

    <h2 style="margin-top: 36px; margin-bottom: 12px;">3. COMPENSATION</h2>
    <p style="margin-bottom: 24px;">The Client agrees to pay the Service Provider a fee of $10,000 per month for the Services. Payment shall be made within 30 days of receipt of an invoice from the Service Provider.</p>

    <h2 style="margin-top: 36px; margin-bottom: 12px;">4. CONFIDENTIALITY</h2>
    <p style="margin-bottom: 24px;">Both parties agree to maintain the confidentiality of any proprietary information disclosed during the course of this Agreement.</p>

    <h2 style="margin-top: 36px; margin-bottom: 12px;">5. INTELLECTUAL PROPERTY</h2>
    <p style="margin-bottom: 24px;">All intellectual property created by the Service Provider in the course of providing the Services shall be owned by the Client upon full payment of all fees due under this Agreement.</p>

    <div style="margin-top: 60px; display: flex; justify-content: space-between;">
      <div style="width: 45%;">
        <p style="margin-bottom: 36px;">________________________</p>
        <p>Authorized Signature (Client)</p>
        <p>Name: John Smith</p>
        <p>Title: CEO</p>
        <p>Date: ________________</p>
      </div>
      <div style="width: 45%;">
        <p style="margin-bottom: 36px;">________________________</p>
        <p>Authorized Signature (Service Provider)</p>
        <p>Name: Jane Doe</p>
        <p>Title: CEO</p>
        <p>Date: ________________</p>
      </div>
    </div>`
  );

  const previewRef = useRef<HTMLDivElement>(null);
  const editorRef = useRef<HTMLDivElement>(null);

  // Handle save
  const handleSave = () => {
    if (editorRef.current) {
      const newContent = editorRef.current.innerHTML;
      setContent(newContent);
      if (onSave) {
        onSave(newContent);
      }
      setIsEditing(false);
    }
  };

  // Handle editor command
  const handleCommand = (command: string, value: string = '') => {
    document.execCommand(command, false, value);
    if (editorRef.current) {
      editorRef.current.focus();
    }
  };

  // Toggle editing mode
  const toggleEditMode = () => {
    if (isEditing && editorRef.current) {
      // If we're currently editing, save the content before switching to preview
      const newContent = editorRef.current.innerHTML;
      setContent(newContent);
      if (onSave) {
        onSave(newContent);
      }
    }
    setIsEditing(!isEditing);
  };

  // Handle PDF export
  const handleExportPDF = () => {
    // In a real implementation, this would use a PDF generation library
    // For now, we'll just log to console
    console.log("Exporting PDF of contract:", contractId);
    alert("PDF export functionality would be implemented here");
  };

  return (
    <div className="w-full h-full flex flex-col">
      <Card className="flex-1 flex flex-col overflow-hidden border-slate-200">
        <div className="border-b px-6 py-2 bg-muted/30 flex items-center justify-between">
          <div className="text-sm font-medium">Document Editor</div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={toggleEditMode}
              className="h-8"
            >
              {isEditing ? (
                <>
                  <Eye className="h-4 w-4 mr-2" />
                  Preview
                </>
              ) : (
                <>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </>
              )}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleExportPDF}
              className="h-8"
            >
              <Download className="h-4 w-4 mr-2" />
              Export PDF
            </Button>
          </div>
        </div>

        {isEditing ? (
          <div className="border-b px-6 py-2 bg-muted/30 flex items-center gap-1 overflow-x-auto">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => handleCommand('bold')}>
                    <Bold className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Bold</TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => handleCommand('italic')}>
                    <Italic className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Italic</TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => handleCommand('underline')}>
                    <Underline className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Underline</TooltipContent>
              </Tooltip>

              <Separator orientation="vertical" className="mx-1 h-6" />

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => handleCommand('justifyLeft')}>
                    <AlignLeft className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Align Left</TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => handleCommand('justifyCenter')}>
                    <AlignCenter className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Align Center</TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => handleCommand('justifyRight')}>
                    <AlignRight className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Align Right</TooltipContent>
              </Tooltip>

              <Separator orientation="vertical" className="mx-1 h-6" />

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => handleCommand('insertUnorderedList')}>
                    <List className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Bullet List</TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => handleCommand('insertOrderedList')}>
                    <ListOrdered className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Numbered List</TooltipContent>
              </Tooltip>

              <Separator orientation="vertical" className="mx-1 h-6" />

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => handleCommand('formatBlock', '<h1>')}>
                    <Heading1 className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Heading 1</TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => handleCommand('formatBlock', '<h2>')}>
                    <Heading2 className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Heading 2</TooltipContent>
              </Tooltip>

              <Separator orientation="vertical" className="mx-1 h-6" />

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => handleCommand('undo')}>
                    <Undo className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Undo</TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => handleCommand('redo')}>
                    <Redo className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Redo</TooltipContent>
              </Tooltip>

              <div className="flex-1"></div>

              <Button variant="default" size="sm" onClick={handleSave} className="h-8">
                <Save className="h-4 w-4 mr-2" />
                Save
              </Button>
            </TooltipProvider>
          </div>
        ) : null}

        <CardContent className="flex-1 p-0 overflow-auto">
          {isEditing ? (
            <div
              ref={editorRef}
              className="min-h-full p-8 focus:outline-none"
              contentEditable
              dangerouslySetInnerHTML={{ __html: content }}
              style={{
                fontFamily: '"Times New Roman", Times, serif',
                fontSize: '12pt',
                lineHeight: '1.5',
                color: '#333'
              }}
            />
          ) : (
            <div
              ref={previewRef}
              className="min-h-full p-8"
              style={{
                fontFamily: '"Times New Roman", Times, serif',
                fontSize: '12pt',
                lineHeight: '1.5',
                color: '#333'
              }}
              dangerouslySetInnerHTML={{ __html: content }}
            />
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default ContractReviewPreview;
