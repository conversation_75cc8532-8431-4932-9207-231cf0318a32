import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { ContractService, TemplateService } from '@/services/api-services';
import { useAuth } from '@clerk/clerk-react';
import type { Template } from '@/services/api-types';
import {
  Download,
  FileText,
  File,
  Globe,
  Code,
  Loader2,
  CheckCircle,
  AlertCircle,
} from 'lucide-react';

interface DocumentExportButtonProps {
  contractId: string;
  contractTitle?: string;
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'default' | 'sm' | 'lg';
  className?: string;
  showLabel?: boolean;
  enableTemplateSelection?: boolean;
  workspaceId?: string;
}

type ExportFormat = 'pdf' | 'docx' | 'html' | 'txt' | 'markdown';

interface ExportOption {
  format: ExportFormat;
  label: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  contentType: string;
}

const exportOptions: ExportOption[] = [
  {
    format: 'pdf',
    label: 'PDF Document',
    description: 'Professional PDF with formatting',
    icon: FileText,
    contentType: 'application/pdf',
  },
  {
    format: 'docx',
    label: 'Word Document',
    description: 'Editable Microsoft Word format',
    icon: File,
    contentType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  },
  {
    format: 'html',
    label: 'HTML Document',
    description: 'Web-ready HTML format',
    icon: Globe,
    contentType: 'text/html',
  },
  {
    format: 'txt',
    label: 'Plain Text',
    description: 'Simple text format',
    icon: FileText,
    contentType: 'text/plain',
  },
  {
    format: 'markdown',
    label: 'Markdown',
    description: 'Markdown format for documentation',
    icon: Code,
    contentType: 'text/markdown',
  },
];

const DocumentExportButton: React.FC<DocumentExportButtonProps> = ({
  contractId,
  contractTitle = 'Contract',
  variant = 'outline',
  size = 'default',
  className = '',
  showLabel = true,
  enableTemplateSelection = false,
  workspaceId,
}) => {
  const [isExporting, setIsExporting] = useState<ExportFormat | null>(null);
  const [exportStatus, setExportStatus] = useState<{
    format: ExportFormat;
    status: 'success' | 'error';
    message: string;
  } | null>(null);
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);
  const [availableTemplates, setAvailableTemplates] = useState<Template[]>([]);
  const [isLoadingTemplates, setIsLoadingTemplates] = useState(false);
  const { toast } = useToast();
  const { getToken } = useAuth();

  // Load templates when template selection is enabled
  React.useEffect(() => {
    if (enableTemplateSelection && workspaceId) {
      loadTemplates();
    }
  }, [enableTemplateSelection, workspaceId]);

  const loadTemplates = async () => {
    if (!workspaceId) return;

    setIsLoadingTemplates(true);
    try {
      const token = await getToken();
      const response = await TemplateService.getTemplates(workspaceId, {}, token || undefined);

      if (response.success && response.data) {
        setAvailableTemplates(response.data);
      }
    } catch (error) {
      console.error('Failed to load templates:', error);
    } finally {
      setIsLoadingTemplates(false);
    }
  };

  const handleExport = async (format: ExportFormat) => {
    setIsExporting(format);
    setExportStatus(null);

    try {
      // Get auth token
      const token = await getToken();

      // Generate document
      const response = await ContractService.generateDocument(
        contractId,
        format,
        selectedTemplate || undefined, // template name
        token || undefined
      );

      if (response.success && response.data) {
        const { file_info } = response.data;
        
        // Create download link
        const link = document.createElement('a');
        link.href = file_info.url;
        link.download = file_info.filename;
        link.target = '_blank';
        
        // Trigger download
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Show success message
        setExportStatus({
          format,
          status: 'success',
          message: `${format.toUpperCase()} exported successfully`,
        });

        toast({
          title: 'Export Successful',
          description: `${contractTitle} has been exported as ${format.toUpperCase()}`,
        });

        // Clear status after 3 seconds
        setTimeout(() => setExportStatus(null), 3000);
      } else {
        throw new Error(response.message || 'Export failed');
      }
    } catch (error: any) {
      console.error('Export error:', error);
      
      const errorMessage = error.message || `Failed to export ${format.toUpperCase()}`;
      
      setExportStatus({
        format,
        status: 'error',
        message: errorMessage,
      });

      toast({
        title: 'Export Failed',
        description: errorMessage,
        variant: 'destructive',
      });

      // Clear status after 5 seconds
      setTimeout(() => setExportStatus(null), 5000);
    } finally {
      setIsExporting(null);
    }
  };

  const getStatusIcon = (format: ExportFormat) => {
    if (isExporting === format) {
      return <Loader2 className="h-4 w-4 animate-spin" />;
    }
    
    if (exportStatus?.format === format) {
      return exportStatus.status === 'success' 
        ? <CheckCircle className="h-4 w-4 text-green-500" />
        : <AlertCircle className="h-4 w-4 text-red-500" />;
    }
    
    return null;
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          variant={variant} 
          size={size} 
          className={className}
          disabled={isExporting !== null}
        >
          {isExporting ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <Download className="h-4 w-4" />
          )}
          {showLabel && (
            <span className="ml-2">
              {isExporting ? 'Exporting...' : 'Export'}
            </span>
          )}
        </Button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent align="end" className="w-64">
        <DropdownMenuLabel className="flex items-center gap-2">
          <Download className="h-4 w-4" />
          Export Document
        </DropdownMenuLabel>
        <DropdownMenuSeparator />

        {/* Template Selection */}
        {enableTemplateSelection && (
          <>
            <DropdownMenuLabel className="text-xs text-muted-foreground">
              Template (Optional)
            </DropdownMenuLabel>
            <div className="px-2 pb-2">
              <select
                value={selectedTemplate || ''}
                onChange={(e) => setSelectedTemplate(e.target.value || null)}
                className="w-full text-xs p-1 border rounded"
                disabled={isLoadingTemplates}
              >
                <option value="">Default Template</option>
                {availableTemplates.map((template) => (
                  <option key={template.id} value={template.title}>
                    {template.title}
                  </option>
                ))}
              </select>
              {isLoadingTemplates && (
                <div className="text-xs text-muted-foreground mt-1">
                  Loading templates...
                </div>
              )}
            </div>
            <DropdownMenuSeparator />
          </>
        )}
        
        {exportOptions.map((option) => {
          const Icon = option.icon;
          const statusIcon = getStatusIcon(option.format);
          const isCurrentlyExporting = isExporting === option.format;
          const hasStatus = exportStatus?.format === option.format;
          
          return (
            <DropdownMenuItem
              key={option.format}
              onClick={() => handleExport(option.format)}
              disabled={isExporting !== null}
              className="flex items-center gap-3 p-3 cursor-pointer"
            >
              <div className="flex items-center gap-3 flex-1">
                <Icon className="h-4 w-4 text-muted-foreground" />
                <div className="flex-1">
                  <div className="font-medium">{option.label}</div>
                  <div className="text-xs text-muted-foreground">
                    {option.description}
                  </div>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                {hasStatus && (
                  <Badge 
                    variant={exportStatus?.status === 'success' ? 'default' : 'destructive'}
                    className="text-xs"
                  >
                    {exportStatus?.status === 'success' ? 'Done' : 'Failed'}
                  </Badge>
                )}
                {statusIcon}
              </div>
            </DropdownMenuItem>
          );
        })}
        
        <DropdownMenuSeparator />
        <div className="p-2 text-xs text-muted-foreground">
          Documents are generated with professional formatting and branding
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default DocumentExportButton;
