import os
import sys
from pathlib import Path

def update_frontend_api():
    """
    Update the frontend API configuration to use the FastAPI backend.
    """
    try:
        # Path to the frontend API file
        api_file_path = Path(__file__).parent.parent / "src" / "lib" / "api.ts"
        
        if not api_file_path.exists():
            print(f"Error: API file not found at {api_file_path}")
            return False
        
        # Read the current API file
        with open(api_file_path, "r") as f:
            api_content = f.read()
        
        # Check if the file already uses the real API
        if "// Using real API" in api_content:
            print("Frontend API is already configured to use the real API.")
            return True
        
        # Replace the mock API with the real API
        new_api_content = """import { useError } from "./error-provider";
import { useLoading } from "./loading-provider";

// API response types
export interface ApiResponse<T> {
  data: T;
  status: number;
  message: string;
}

export interface ApiError {
  status: number;
  message: string;
  details?: string;
  code?: string;
}

// Base API service with error handling
export class ApiService {
  private baseUrl: string;
  
  constructor(baseUrl = "/api") {
    this.baseUrl = baseUrl;
  }
  
  // Generic request method with error handling
  async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      const url = `${this.baseUrl}${endpoint}`;
      
      // Set default headers
      const headers = {
        "Content-Type": "application/json",
        ...options.headers,
      };
      
      // Make the request
      const response = await fetch(url, {
        ...options,
        headers,
      });
      
      // Parse the response
      const data = await response.json();
      
      // Check if the response is successful
      if (!response.ok) {
        throw {
          status: response.status,
          message: data.message || "An error occurred",
          details: data.details,
          code: data.code,
        };
      }
      
      return {
        data,
        status: response.status,
        message: data.message || "Success",
      };
    } catch (error) {
      // Format the error
      const apiError: ApiError = {
        status: (error as any)?.status || 500,
        message: (error as any)?.message || "An unexpected error occurred",
        details: (error as any)?.details,
        code: (error as any)?.code,
      };
      
      throw apiError;
    }
  }
  
  // Convenience methods for different HTTP methods
  async get<T>(endpoint: string, options: RequestInit = {}): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: "GET" });
  }
  
  async post<T>(
    endpoint: string,
    data: any,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      ...options,
      method: "POST",
      body: JSON.stringify(data),
    });
  }
  
  async put<T>(
    endpoint: string,
    data: any,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      ...options,
      method: "PUT",
      body: JSON.stringify(data),
    });
  }
  
  async delete<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: "DELETE" });
  }
}

// Using real API
export const api = new ApiService();

// Hook for using the API with loading and error handling
export function useApi() {
  const { handleApiError } = useError();
  const { withLoading } = useLoading();
  
  return {
    // Wrap API calls with loading and error handling
    async fetch<T>(
      apiCall: () => Promise<ApiResponse<T>>,
      loadingMessage = "Loading...",
      errorMessage = "Failed to fetch data"
    ): Promise<T | null> {
      try {
        const response = await withLoading(apiCall(), loadingMessage);
        return response.data;
      } catch (error) {
        handleApiError(error, errorMessage);
        return null;
      }
    },
    
    // Raw API service for more control
    api,
  };
}

// For development and testing, we also provide a mock API
export class MockApiService extends ApiService {
  private mockData: Record<string, any>;
  private delay: number;
  
  constructor({ mockData = {}, delay = 500 }: { mockData?: Record<string, any>; delay?: number }) {
    super();
    this.mockData = mockData;
    this.delay = delay;
  }
  
  // Override request method to use mock data
  async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    // Simulate network delay
    await new Promise((resolve) => setTimeout(resolve, this.delay));
    
    // Get the mock data for this endpoint
    const mockEndpoint = endpoint.split("?")[0]; // Remove query params
    const data = this.mockData[mockEndpoint];
    
    // Simulate error if no mock data is found
    if (!data && options.method !== "POST") {
      throw {
        status: 404,
        message: `No mock data found for ${mockEndpoint}`,
      };
    }
    
    // Return the mock data
    return {
      data: data as T,
      status: 200,
      message: "Success",
    };
  }
  
  // Add mock data
  addMockData(endpoint: string, data: any): void {
    this.mockData[endpoint] = data;
  }
}

// Create a mock API instance for frontend development
export const mockApi = new MockApiService({
  // Add your mock data here
  "/contracts": [
    {
      id: "c1",
      title: "Service Agreement with Acme Corp",
      type: "Service Agreement",
      status: "active",
    },
    {
      id: "c2",
      title: "Non-Disclosure Agreement with TechStart",
      type: "NDA",
      status: "active",
    },
  ],
});
"""
        
        # Write the updated API file
        with open(api_file_path, "w") as f:
            f.write(new_api_content)
        
        print("Frontend API updated successfully to use the real API.")
        return True
    
    except Exception as e:
        print(f"Error updating frontend API: {e}")
        return False

if __name__ == "__main__":
    print("Updating frontend API configuration...")
    success = update_frontend_api()
    
    if success:
        print("Frontend API configuration updated successfully.")
    else:
        print("Failed to update frontend API configuration.")
        sys.exit(1)
