#!/usr/bin/env python3
"""
Test script to verify PDF generation functionality with ReportLab fallback.
"""

import asyncio
import sys
import os
from datetime import datetime

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

from app.services.document_generator import DocumentGeneratorService, WEASYPRINT_AVAILABLE

async def test_pdf_generation():
    """Test PDF generation with mock contract data."""
    
    print("🔍 Testing PDF Generation Functionality")
    print("=" * 50)
    
    # Check which PDF backend is being used
    if WEASYPRINT_AVAILABLE:
        print("✅ Using WeasyPrint for PDF generation")
    else:
        print("✅ Using ReportLab for PDF generation (WeasyPrint fallback)")
    
    # Create mock contract data
    mock_contract_data = {
        "id": "test-contract-123",
        "title": "Test Service Agreement",
        "type": "service_agreement",
        "workspace_id": "default-workspace",
        "description": "This is a test contract to verify PDF generation functionality with ReportLab.",
        "parties": [
            {
                "name": "LegalAI Inc.",
                "type": "company",
                "address": "123 Tech Street, San Francisco, CA 94105",
                "representative": "<PERSON>",
                "title": "CEO",
                "role": "Service Provider"
            },
            {
                "name": "Test Client Corp.",
                "type": "company", 
                "address": "456 Business Ave, New York, NY 10001",
                "representative": "Jane Doe",
                "title": "CTO",
                "role": "Client"
            }
        ],
        "clauses": [
            {
                "title": "Service Description",
                "content": "The Service Provider agrees to provide comprehensive contract management services to the Client, including document generation, storage, and workflow management."
            },
            {
                "title": "Payment Terms",
                "content": "Client agrees to pay Service Provider the agreed-upon fees within 30 days of invoice receipt. Late payments may incur additional charges."
            }
        ],
        "standard_clauses": ["confidentiality", "limitation_of_liability", "termination"],
        "custom_clauses": [
            "This agreement may be modified only by written consent of both parties."
        ],
        "effective_date": "2024-01-01T00:00:00Z",
        "expiry_date": "2024-12-31T23:59:59Z",
        "jurisdiction": "California, USA",
        "created_at": datetime.now().isoformat()
    }
    
    print(f"📄 Mock Contract: {mock_contract_data['title']}")
    print(f"🏢 Parties: {len(mock_contract_data['parties'])}")
    print(f"📋 Clauses: {len(mock_contract_data['clauses'])} + {len(mock_contract_data['standard_clauses'])} standard")
    print()
    
    try:
        # Initialize document generator
        print("🔧 Initializing DocumentGeneratorService...")
        doc_generator = DocumentGeneratorService()
        print("✅ DocumentGeneratorService initialized successfully")
        
        # Test PDF generation
        print("📄 Generating PDF document...")
        
        branding_options = {
            "company_name": "LegalAI Test Suite",
            "letterhead": True,
            "footer_text": "Generated by LegalAI Advanced Document Generator - Test Mode",
            "color_scheme": {
                "primary": "#1e293b",
                "secondary": "#64748b",
                "accent": "#3b82f6"
            }
        }
        
        file_info = await doc_generator.generate_contract_document(
            contract_data=mock_contract_data,
            format_type="pdf",
            branding_options=branding_options
        )
        
        print("✅ PDF generation successful!")
        print(f"📁 File: {file_info['filename']}")
        print(f"📏 Size: {file_info['size']} bytes")
        print(f"🔗 URL: {file_info['url']}")
        print(f"📍 Path: {file_info['path']}")
        print(f"⏰ Generated: {file_info['generated_at']}")
        
        # Test other formats
        print("\n🔄 Testing other document formats...")
        
        formats_to_test = ["docx", "html", "txt", "markdown"]
        
        for format_type in formats_to_test:
            try:
                print(f"  📄 Generating {format_type.upper()}...")
                format_file_info = await doc_generator.generate_contract_document(
                    contract_data=mock_contract_data,
                    format_type=format_type,
                    branding_options=branding_options
                )
                print(f"  ✅ {format_type.upper()} generation successful! ({format_file_info['size']} bytes)")
            except Exception as e:
                print(f"  ❌ {format_type.upper()} generation failed: {str(e)}")
        
        print("\n🎉 Document generation test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ PDF generation failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Starting PDF Generation Test")
    print("=" * 50)
    
    # Run the test
    success = asyncio.run(test_pdf_generation())
    
    if success:
        print("\n✅ All tests passed! PDF generation is working correctly.")
        sys.exit(0)
    else:
        print("\n❌ Tests failed! Please check the errors above.")
        sys.exit(1)
