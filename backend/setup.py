import os
import sys
import subprocess
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def setup_backend():
    """
    Set up the backend by installing dependencies and initializing the database.
    """
    try:
        print("Setting up the backend...")
        
        # Check if Python is installed
        try:
            subprocess.run(["python", "--version"], check=True, capture_output=True)
        except:
            print("Error: Python is not installed or not in PATH.")
            return False
        
        # Check if pip is installed
        try:
            subprocess.run(["pip", "--version"], check=True, capture_output=True)
        except:
            print("Error: pip is not installed or not in PATH.")
            return False
        
        # Install dependencies
        print("Installing dependencies...")
        subprocess.run(["pip", "install", "-r", "requirements.txt"], check=True)
        
        # Check if .env file exists
        env_file = Path(__file__).parent / ".env"
        if not env_file.exists():
            # Create .env file from .env.example
            env_example = Path(__file__).parent / ".env.example"
            if env_example.exists():
                print("Creating .env file from .env.example...")
                with open(env_example, "r") as f:
                    env_content = f.read()
                
                with open(env_file, "w") as f:
                    f.write(env_content)
                
                print("Created .env file. Please update it with your Supabase and Clerk credentials.")
            else:
                print("Error: .env.example file not found.")
                return False
        
        # Initialize the database
        print("Initializing the database...")
        from app.db.init_db import init_db
        init_db()
        
        # Update frontend API configuration
        print("Updating frontend API configuration...")
        from update_frontend_api import update_frontend_api
        update_frontend_api()
        
        # Update Vite configuration
        print("Updating Vite configuration...")
        from update_vite_config import update_vite_config
        update_vite_config()
        
        print("Backend setup completed successfully.")
        return True
    
    except Exception as e:
        print(f"Error setting up backend: {e}")
        return False

if __name__ == "__main__":
    print("Setting up the LegalAI backend...")
    success = setup_backend()
    
    if success:
        print("""
Backend setup completed successfully!

To run the backend:
1. Make sure you have updated the .env file with your Supabase and Clerk credentials
2. Run the backend server: python run.py
3. The API will be available at http://localhost:8000
4. API documentation will be available at http://localhost:8000/api/docs

To run the frontend with the backend:
1. In a separate terminal, navigate to the project root
2. Run the frontend: npm run dev
3. The frontend will be available at http://localhost:5173
4. API requests will be proxied to the backend
""")
    else:
        print("Backend setup failed.")
        sys.exit(1)
