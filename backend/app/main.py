from fastapi import FastAP<PERSON>, Depends
from fastapi.middleware.cors import CORSMiddleware
from app.core.config import settings
from app.api.api_v1.api import api_router
from app.db.database import get_supabase_client
from app.services.storage import StorageService
from app.core.auth import get_current_user

app = FastAPI(
    title="LegalAI API",
    description="API for LegalAI Contract Management System",
    version="1.0.0",
    openapi_url=f"{settings.API_PREFIX}/openapi.json",
    docs_url=f"{settings.API_PREFIX}/docs",
    redoc_url=f"{settings.API_PREFIX}/redoc",
)

# Set up CORS middleware
if settings.BACKEND_CORS_ORIGINS:
    app.add_middleware(
        CORSMiddleware,
        allow_origins=[str(origin) for origin in settings.BACKEND_CORS_ORIGINS],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

# Include API router
app.include_router(api_router, prefix=settings.API_PREFIX)

# Initialize storage on startup
@app.on_event("startup")
async def startup_event():
    """Initialize services on application startup."""
    # Initialize storage buckets
    await StorageService.initialize_storage()

@app.get("/")
async def root():
    return {"message": "Welcome to LegalAI API. Visit /api/docs for documentation."}

@app.get("/api/auth/test")
async def test_auth(current_user: dict = Depends(get_current_user)):
    """
    Test endpoint to verify authentication is working.
    """
    return {
        "message": "Authentication successful!",
        "user": {
            "id": current_user.get("id"),
            "email": current_user.get("email", ""),
            "name": f"{current_user.get('first_name', '')} {current_user.get('last_name', '')}".strip(),
            "workspaces": current_user.get("workspaces", [])
        },
        "environment": settings.ENVIRONMENT,
        "clerk_configured": bool(settings.CLERK_SECRET_KEY and settings.CLERK_SECRET_KEY != "sk_test_placeholder_get_from_clerk_dashboard")
    }

@app.get("/api/health")
async def health_check():
    """
    Health check endpoint that verifies the database connection.
    This endpoint doesn't require authentication.
    """
    try:
        # Get Supabase client
        supabase = get_supabase_client()

        # Try a simple query to verify connection
        response = supabase.table("users").select("*").execute()

        # Check if we got a successful response
        if hasattr(response, 'data'):
            user_count = len(response.data)

            # Try to get some workspace data
            workspaces_response = supabase.table("workspaces").select("*").execute()
            workspace_count = len(workspaces_response.data) if hasattr(workspaces_response, 'data') else 0

            # Try to get some contract data
            contracts_response = supabase.table("contracts").select("*").execute()
            contract_count = len(contracts_response.data) if hasattr(contracts_response, 'data') else 0

            # Check storage status
            storage_status = "unknown"
            try:
                # List buckets to verify storage access
                supabase.storage.list_buckets()
                storage_status = "connected"
            except Exception as e:
                storage_status = f"error: {str(e)}"

            return {
                "status": "healthy",
                "database": "connected",
                "storage": storage_status,
                "database_url": settings.SUPABASE_URL,
                "counts": {
                    "users": user_count,
                    "workspaces": workspace_count,
                    "contracts": contract_count
                }
            }
        else:
            return {
                "status": "error",
                "message": "Database connection error",
                "details": "Invalid response from database"
            }
    except Exception as e:
        return {
            "status": "error",
            "message": "Database connection error",
            "details": str(e)
        }
