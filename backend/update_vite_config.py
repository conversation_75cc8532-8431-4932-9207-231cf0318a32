import os
import sys
from pathlib import Path
import re

def update_vite_config():
    """
    Update the Vite configuration to proxy API requests to the FastAPI backend.
    """
    try:
        # Path to the Vite config file
        vite_config_path = Path(__file__).parent.parent / "vite.config.ts"
        
        if not vite_config_path.exists():
            # Try the JavaScript version
            vite_config_path = Path(__file__).parent.parent / "vite.config.js"
            
            if not vite_config_path.exists():
                print("Error: Vite config file not found.")
                return False
        
        # Read the current Vite config
        with open(vite_config_path, "r") as f:
            vite_config = f.read()
        
        # Check if the proxy is already configured
        if "'/api': {" in vite_config:
            print("Vite config is already configured with API proxy.")
            return True
        
        # Find the server configuration
        server_config_pattern = r"server:\s*{([^}]*)}"
        server_config_match = re.search(server_config_pattern, vite_config)
        
        if not server_config_match:
            print("Error: Could not find server configuration in Vite config.")
            return False
        
        # Current server configuration
        current_server_config = server_config_match.group(0)
        
        # New server configuration with proxy
        new_server_config = current_server_config.replace(
            "server: {",
            """server: {
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        secure: false,
      },
    },"""
        )
        
        # Replace the server configuration
        updated_vite_config = vite_config.replace(current_server_config, new_server_config)
        
        # Write the updated Vite config
        with open(vite_config_path, "w") as f:
            f.write(updated_vite_config)
        
        print("Vite config updated successfully with API proxy.")
        return True
    
    except Exception as e:
        print(f"Error updating Vite config: {e}")
        return False

if __name__ == "__main__":
    print("Updating Vite configuration...")
    success = update_vite_config()
    
    if success:
        print("Vite configuration updated successfully.")
    else:
        print("Failed to update Vite configuration.")
        sys.exit(1)
